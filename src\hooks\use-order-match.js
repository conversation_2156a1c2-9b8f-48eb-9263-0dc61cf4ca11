import { reactive } from 'vue';
import { ElMessage } from 'element-plus';
import request from '@/utils/request';

export default opiotns => {
  const { url, form, option, before, after, success, fail } = opiotns;

  const matcher = reactive({
    visible: false,
    loading: false,
    selectedKey: undefined
  });

  function delay(ms) {
    return new Promise(resolve => {
      setTimeout(resolve, ms);
    });
  }

  // 选择要匹配的key
  function matching() {
    if (form.value?.hotelStartName == '' || form.value?.hotelEndName == '') {
      ElMessage.warning('请填写起点地址名称和终点地址名称');
      return;
    }
    matcher.visible = true;
  }

  // 已选择匹配key
  async function matched(e) {
    matcher.selectedKey = e;
    matcher.visible = false;
    await match();
  }

  // 订单地址匹配
  async function match() {
    try {
      if (before && typeof before == 'function') {
        form.value = await before(form.value);
      }
      matcher.loading = true;
      const startName = form.value[matcher.selectedKey.startKey];
      const endName = form.value[matcher.selectedKey.endKey];

      const startHotel = await request.get(url, {
        params: { name: startName }
      });

      await delay(150);

      const endHotel = await request.get(url, {
        params: { name: endName }
      });

      const hasStart = startHotel.data != null;
      const hasEnd = endHotel.data != null;

      if (hasStart) {
        form.value.hotelStartId = startHotel.data.id;
        form.value.hotelStartShortName = startHotel.data.shortName;
        form.value.hotelStartShortNameThai = startHotel.data.shortNameThai;
        form.value.hotelStartAddress = startHotel.data.address;
        form.value.hotelStartLevel = startHotel.data.level;
      }
      if (hasEnd) {
        form.value.hotelEndId = endHotel.data.id;
        form.value.hotelEndShortName = endHotel.data.shortName;
        form.value.hotelEndShortNameThai = endHotel.data.shortNameThai;
        form.value.hotelEndAddress = endHotel.data.address;
        form.value.hotelEndLevel = endHotel.data.level;
      }
      if (hasStart && hasEnd) {
        const startShortName = startHotel.data.shortName;
        const endShortName = endHotel.data.shortName;
        const shortMerge = `${startShortName}-${endShortName}`;
        const route = await request.get('/youyang/route/match', {
          params: { name: shortMerge }
        });
        if (route.data != null) {
          form.value.routeId = route.data.id;
          form.value.routeName = route.data.name;
          if (after && typeof after == 'function') {
            await after(route);
          }
          success({
            startHotel,
            endHotel,
            form: form.value,
            selectedKey: matcher.selectedKey,
            route
          });
        } else {
          fail({
            startHotel,
            endHotel,
            form: form.value,
            selectedKey: matcher.selectedKey
          });
        }
      } else {
        fail({
          startHotel,
          endHotel,
          form: form.value,
          selectedKey: matcher.selectedKey
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      matcher.loading = false;
    }
  }

  // 接机航班必填
  function setFlightRules(newValue) {
    if (newValue == 1) {
      option.value.column[4].rules = [
        { required: true, message: '必填的' },
        { max: 12, message: '不能超过12个字符' }
      ];
    } else {
      option.value.column[4].rules = undefined;
    }
  }

  return { matcher, matching, matched, setFlightRules };
};
