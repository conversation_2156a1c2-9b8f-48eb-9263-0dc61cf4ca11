// 微信图标
import wechatGrey from '@/assets/icons/color/wechat_grey.png';
import wechatRed from '@/assets/icons/color/wechat_red.png';
import wechatYellow from '@/assets/icons/color/wechat_yellow.png';
import wechatGreen from '@/assets/icons/color/wechat_green.png';
// 电话图标
import phoneGrey from '@/assets/icons/color/phone_grey.png';
import phoneYellow from '@/assets/icons/color/phone_yellow.png';
import phoneGreen from '@/assets/icons/color/phone_green.png';
import phoneeRed from '@/assets/icons/color/phone_red.png';
// 车辆图标
import vehicleGrey from '@/assets/icons/color/vehicle_grey.png';
import vehicleGreen from '@/assets/icons/color/vehicle_green.png';
// IM图标
import imGrey from '@/assets/icons/color/im_grey.png';
import imGreen from '@/assets/icons/color/im_green.png';
// 同步
import syncBlack from '@/assets/icons/color/sync_black.png';
import syncGreen from '@/assets/icons/color/sync_green.png';
// 回访
import visitGrey from '@/assets/icons/color/visit_grey.png';
import visitGreen from '@/assets/icons/color/visit_green.png';
// 请求
import request from '@/utils/request';
import { ElMessage } from 'element-plus';

export default () => {
  const phoneKey = 'passengerPhoneColor';
  const wechatKey = 'passengerWechatColor';
  const imKey = 'passengerImColor';
  const arrivedKey = 'arrived';
  const syncKey = 'syncColor';
  const visitKey = 'visitColor';
  const exitKey = 'exitPort';

  // 4种颜色
  function getPhoneIcon(row) {
    const colors = [phoneGrey, phoneYellow, phoneGreen, phoneeRed];
    return colors[row[phoneKey]];
  }

  // 4种颜色
  function getWechatIcon(row) {
    const colors = [wechatGrey, wechatYellow, wechatGreen, wechatRed];
    return colors[row[wechatKey]];
  }

  // 3种颜色
  function getVehicleIcon(row) {
    const colors = [vehicleGrey, vehicleGrey, vehicleGreen];
    return colors[row[arrivedKey]];
  }

  // 2种颜色
  function getImIcon(row) {
    const colors = [imGrey, imGreen];
    return colors[row[imKey]];
  }

  // 2种颜色
  function getSyncIcon(row) {
    const colors = [syncBlack, syncGreen];
    return colors[row[syncKey]];
  }

  // 2种颜色
  function getVisitIcon(row) {
    const colors = [visitGrey, visitGreen];
    return colors[row[visitKey]];
  }

  // 切换手机图标颜色：0、1、2、3
  async function setPhoneIcon(row) {
    const color = row[phoneKey];
    let value = color;
    if (color == 3) {
      value = 0;
    } else {
      value++;
    }
    await set(row, phoneKey, value);
  }

  // 切换微信图标颜色：0、1、2、3
  async function setWechatIcon(row) {
    const color = row[wechatKey];
    let value = color;
    if (color == 3) {
      value = 0;
    } else {
      value++;
    }
    await set(row, wechatKey, value);
  }

  // 切换车辆图标颜色：1、2
  async function setVehicleIcon(row) {
    const color = row[arrivedKey];
    const value = color == 2 ? 1 : 2;
    await set(row, arrivedKey, value);
  }

  // 设置IM图标颜色：0、1
  async function setImIcon(row) {
    const color = row[imKey];
    const value = color == 1 ? 0 : 1;
    await set(row, imKey, value);
  }

  // 设置同步图标颜色：0、1
  async function setSyncIcon(row) {
    const color = row[syncKey];
    const value = color == 1 ? 0 : 1;
    await set(row, syncKey, value);
  }

  // 设置回访图标颜色：0、1
  async function setVisitIcon(row) {
    const color = row[visitKey];
    const value = color == 1 ? 0 : 1;
    await set(row, visitKey, value);
  }

  // 设置出站口
  async function setExitPort(row, value) {
    await set(row, exitKey, value);
  }

  // 设置属性
  async function set(row, prop, value) {
    try {
      const { data } = await request.put('/youyang/order/set', {
        id: row.id,
        prop,
        value
      });
      ElMessage.success(data);
      row[prop] = value;
    } catch (error) {
      console.error(error);
    }
  }

  return {
    getWechatIcon,
    getPhoneIcon,
    getVehicleIcon,
    getImIcon,
    getSyncIcon,
    getVisitIcon,
    setWechatIcon,
    setPhoneIcon,
    setVehicleIcon,
    setImIcon,
    setSyncIcon,
    setVisitIcon,
    setExitPort,
    set
  };
};
