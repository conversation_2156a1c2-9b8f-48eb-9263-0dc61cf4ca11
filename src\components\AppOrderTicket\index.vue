<template>
  <div class="app-order-ticket">
    <div class="app-order-ticket__header">
      <div class="app-order-ticket__left">
        <div class="app-order-ticket__success">Confirmed 预定成功</div>
        <div class="app-order-ticket__date-index">{{ dateIndex }}</div>
      </div>
      <div class="app-order-ticket__right">
        <el-image
          class="app-order-ticket__qrcode"
          :src="qrcodeImg"
          alt="qrcode"
        />
        <div class="app-order-ticket__24h">24小时中文客服联系方式</div>
        <div class="app-order-ticket__contact">
          中国拨打：19167036690；②本地拨打：0822163931
        </div>
      </div>
    </div>
    <table>
      <tr>
        <td>
          <div>Booking No</div>
          <div>订单号</div>
        </td>
        <td>{{ sale }}</td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td>
          <div>Time</div>
          <div>集合时间</div>
        </td>
        <td>{{ productAssemblingTime }}</td>
      </tr>
      <tr>
        <td>
          <div>Booking Name</div>
          <div>预订人姓名</div>
        </td>
        <td>
          {{ passengerName }}
          <template v-if="hidePinyin" style="margin-left: 12px">
            {{ passengerPinyin }}
          </template>
        </td>
      </tr>
      <tr>
        <td>
          <div>Number</div>
          <div>预定数量</div>
        </td>
        <td>成人 Audit x {{ number }}，儿童 Child x {{ childrenNumber }}</td>
      </tr>
      <tr>
        <td>
          <div>Name + Passort</div>
          <div>姓名 + 护照</div>
        </td>
        <td>
          {{ passengerName }}
          <template v-if="hidePinyin" style="margin-left: 12px">
            {{ passengerPinyin }}
          </template>
          /
          {{ passengerPassport }}
        </td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td>
          <div>Product</div>
          <div>产品</div>
        </td>
        <td>{{ productName }}</td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td>
          <div>Package</div>
          <div>套餐</div>
        </td>
        <td>{{ productPackage }}</td>
      </tr>
      <tr>
        <td>
          <div>Address</div>
          <div>地址</div>
        </td>
        <td>{{ productAssemblingPlace }}</td>
      </tr>
      <tr>
        <td>
          <div>Meeting Point</div>
          <div>起点地址</div>
        </td>
        <td>{{ hotelStartAddress }}</td>
      </tr>
      <tr>
        <td>
          <div>Send Back</div>
          <div>终点地址</div>
        </td>
        <td>{{ hotelEndAddress }}</td>
      </tr>
      <tr>
        <td>
          <div>Remark</div>
          <div>备注</div>
        </td>
        <td>{{ passengerRemark }}</td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td class="app-order-ticket__center" colspan="2">
          Important Reminder 重要提醒
        </td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td class="app-order-ticket__center" colspan="2">
          {{ productWarning }}
        </td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td class="app-order-ticket__center" colspan="2">Notice 注意事项</td>
      </tr>
      <tr class="app-order-ticket__danger">
        <td class="app-order-ticket__center" colspan="2">{{ productNotes }}</td>
      </tr>
    </table>
  </div>
</template>

<script setup>
import qrcodeImg from '@/assets/images/qrcode.jpeg';
import { toRefs } from 'vue';

defineOptions({ name: 'AppOrderTicket', inheritAttrs: false });

const props = defineProps({
  dateIndex: Number,
  sale: String,
  productAssemblingTime: String,
  passenger: String,
  passengerPinyin: String,
  number: Number,
  childrenNumber: String,
  passengerName: String,
  passengerPassport: String,
  productName: String,
  productPackage: String,
  productAssemblingPlace: String,
  hotelStartAddress: String,
  hotelEndAddress: String,
  passengerRemark: String,
  productWarning: String,
  productNotes: String
});

const { passengerName } = toRefs(props);

// 隐藏拼音
const hidePinyin = computed(() => {
  const firstChar = passengerName.value?.charAt(0);
  if (
    (firstChar >= 'A' && firstChar <= 'Z') ||
    (firstChar >= 'a' && firstChar <= 'z')
  ) {
    return false;
  } else {
    return true;
  }
});
</script>

<style lang="scss" scoped>
.app-order-ticket {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  width: 700px;

  &__center {
    text-align: center;
  }

  &__header {
    display: flex;
    justify-content: space-between;
  }

  &__left {
    flex: 0 0 auto;
  }

  &__right {
    flex: 1 0;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  &__success {
    color: #00b050;
    font-size: 20px;
  }

  &__date-index {
    padding: 12px;
    color: red;
    font-size: 24px;
  }

  &__qrcode {
    width: 100px;
    height: 100x;
    margin-bottom: 6px;
  }

  &__24h {
    margin-bottom: 6px;
    font-size: 18px;
  }

  &__contact {
    margin-bottom: 12px;
    font-size: 14px;
  }

  &__danger {
    color: red;
  }

  table {
    table-layout: fixed;
    border: thin solid black;

    caption {
      display: flex;
      flex-direction: column;
    }

    tr {
      td {
        border: thin solid black;
        padding: 0 20px;
        height: 40px;
        line-height: initial;
        font-size: 14px;

        &:first-child {
          width: 180px;
        }

        div {
          display: flex;
          justify-content: center;
        }
      }
    }
  }
}
</style>
