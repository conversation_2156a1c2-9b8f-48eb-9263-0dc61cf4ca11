<template>
  <div class="app-container order-form">
    <app-result-dialog
      v-model="result.visible"
      v-bind="result"
      @close="resulted"
    />
    <app-order-form
      ref="formRef"
      v-model="form"
      :option="option"
      :loading="matcher.loading"
      @submit="submit"
      @error="error"
      @generate="generate"
      @match="matching"
      @passenger-phone-blur="matchCustomerWechat"
    />
    <app-order-hotel-matcher-dialog
      v-model="matcher.visible"
      v-model:form="form"
      @match="matched"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import useOrderStore from '@/store/modules/order';
import dayjs from 'dayjs';

import { useResult, useOrderFormDialog, useOrderMatch } from '@/hooks';

defineOptions({ name: 'OrderCreate' });

const router = useRouter();

const orderStore = useOrderStore();

// 结果展示对话框
const { result, resulting, resulted } = useResult();

const formRef = ref();

// 新增
const { form, option, submit, error, matchCustomerWechat } = useOrderFormDialog(
  {
    method: 'post',
    defaultForm: { sale: dayjs().format('YYYYMMDDHHmmssSSS') },
    async after() {
      orderStore.form = form.value;
      orderStore.added = true;
      await router.push({ name: 'Order' });
      formRef?.value.resetForm();
    }
  }
);

// 生成订单号时间戳
const generate = () => {
  form.value.sale = dayjs().format('YYYYMMDDHHmmssSSS');
};

// 起点终点地址匹配
const { matcher, matching, matched, setFlightRules } = useOrderMatch({
  url: '/youyang/hotel/match',
  form,
  option,
  success(e) {
    const { startHotel, endHotel, form, selectedKey, route } = e;
    const messages = [];
    const { sale } = form;
    const hotelStartName = form[selectedKey.startKey];
    const hotelEndName = form[selectedKey.endKey];
    const startShortName = startHotel.data.shortName;
    const endShortName = endHotel.data.shortName;
    const routeName = route.data.name;
    messages.push(`订单：${sale}`);
    messages.push(`开始：${hotelStartName}，${startShortName}`);
    messages.push(`结束：${hotelEndName}，${endShortName}`);
    messages.push(`路线：${routeName}`);
    resulting({
      icon: 'success',
      title: '匹配成功',
      subTitle: route.name,
      messages
    });
  },
  fail(e) {
    const { startHotel, endHotel, form, selectedKey } = e;
    const messages = [];
    const { sale } = form;
    const foundText = '没有找到这个地址或没有填写简写';
    const hotelStartName = form[selectedKey.startKey];
    const hotelEndName = form[selectedKey.endKey];
    const startShortName = startHotel.data?.shortName ?? foundText;
    const endShortName = endHotel.data?.shortName ?? foundText;
    messages.push(`订单：${sale}`);
    messages.push(`开始：${hotelStartName}，${startShortName}`);
    messages.push(`结束：${hotelEndName}，${endShortName}`);
    resulting({
      icon: 'error',
      title: '匹配失败',
      subTitle: '到地址管理补充完善',
      messages
    });
  }
});

// 接机航班必填
watch(() => form.value?.type, setFlightRules);
</script>

<style lang="scss" scoped>
.order-form {
  display: flex;
  flex-direction: column;
}
</style>
