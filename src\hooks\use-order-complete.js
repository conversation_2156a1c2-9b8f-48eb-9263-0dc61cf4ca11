import { ElMessage, ElMessageBox } from 'element-plus';
import request from '@/utils/request';

export default options => {
  const { url, before, after } = options;

  // 提交完成
  async function completed(row) {
    try {
      const message = `确定要完成吗？`;
      await ElMessageBox({
        title: '提示',
        message,
        type: 'warning',
        showCancelButton: true,
        draggable: true,
        async beforeClose(action, instance, done) {
          try {
            if (action != 'confirm') {
              done();
              return;
            }
            instance.cancelButtonLoading = true;
            instance.confirmButtonLoading = true;
            // 处理参数
            const data = before(row);
            const response = await request.put(url, data);
            ElMessage.success(response.msg);
            await after();
            done();
          } catch (error) {
            console.error(error);
            instance.cancelButtonLoading = false;
            instance.confirmButtonLoading = false;
          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  }

  return { completed };
};
