<template>
  <!-- <div class="login__header">
    <img
      class="login__logo"
      src="@/assets/logo/logo_long.png"
      alt="logo long"
    />
  </div> -->
  <div class="login__body">
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login__form"
    >
      <h3 class="login__title">{{ shortTitle }}</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="账号"
        >
          <template #prefix>
            <svg-icon icon-class="user" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          placeholder="密码"
          show-password
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon icon-class="password" class="el-input__icon input-icon" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter="handleLogin"
        >
          <template #prefix>
            <svg-icon
              icon-class="validCode"
              class="el-input__icon input-icon"
            />
          </template>
        </el-input>
        <div class="login__code">
          <img :src="codeUrl" @click="getCode" class="login__code-img" />
        </div>
      </el-form-item>
      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          style="width: 100%"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right" v-if="register">
          <router-link class="link-type" :to="'/register'">
            立即注册
          </router-link>
        </div>
      </el-form-item>
    </el-form>
  </div>
  <div class="login__footer">
    <span>{{ copyright }}</span>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import { getCodeImg } from '@/api/login';
import Cookies from 'js-cookie';
import { encrypt, decrypt } from '@/utils/jsencrypt';
import useUserStore from '@/store/modules/user';

const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const shortTitle = import.meta.env.VITE_APP_SHORT_TITLE;

const copyright = import.meta.env.VITE_APP_COPYRIGHT;

const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: ''
});

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(
  route,
  newRoute => {
    redirect.value = newRoute?.query && newRoute.query?.redirect;
  },
  { immediate: true }
);

async function handleLogin() {
  const valid = await proxy.$refs.loginRef.validate();
  if (!valid) {
    return;
  }
  loading.value = true;
  // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
  if (loginForm.value.rememberMe) {
    Cookies.set('username', loginForm.value.username, { expires: 30 });
    Cookies.set('password', encrypt(loginForm.value.password), {
      expires: 30
    });
    Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 });
  } else {
    // 否则移除
    Cookies.remove('username');
    Cookies.remove('password');
    Cookies.remove('rememberMe');
  }
  try {
    // 调用action的登录方法
    await userStore.login(loginForm.value);
    const query = route.query;
    const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
      if (cur !== 'redirect') {
        acc[cur] = query[cur];
      }
      return acc;
    }, {});
    router.push({ path: redirect.value || '/', query: otherQueryParams });
  } catch (error) {
    loading.value = false;
    // 重新获取验证码
    if (captchaEnabled.value) {
      await getCode();
    }
  }
}

async function getCode() {
  const res = await getCodeImg();
  captchaEnabled.value =
    res.captchaEnabled === undefined ? true : res.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + res.img;
    loginForm.value.uuid = res.uuid;
  }
}

function getCookie() {
  const username = Cookies.get('username');
  const password = Cookies.get('password');
  const rememberMe = Cookies.get('rememberMe');
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCode();
getCookie();
</script>

<style lang="scss" scoped>
.login {
  &__header {
    position: absolute;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    padding: 0 64px;
    height: 80px;
    background-color: #ffffffd9;
    backdrop-filter: blur(50px) saturate(180%);
  }

  &__logo {
    height: 60px;
  }

  &__body {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10vw;
    height: 100%;
    background-image: url('../assets/images/login-background.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  &__form {
    border-radius: 6px;
    background: #ffffff;
    width: 400px;
    padding: 25px 25px 5px 25px;
    box-shadow: 0 0 12px 0 rgba(black, 0.2);

    .el-input {
      height: 40px;

      input {
        height: 40px;
      }
    }

    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 0px;
    }
  }

  &__title {
    margin: 0px auto 30px auto;
    color: var(--el-color-primary);
    text-align: center;
    font-weight: bold;
  }

  &__code {
    width: 33%;
    height: 40px;
    float: right;

    &-img {
      height: 40px;
      padding-left: 12px;
      cursor: pointer;
      vertical-align: middle;
    }
  }

  &__remember-me {
    display: flex;
    margin-bottom: 24px;
  }

  &__footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
}
</style>
