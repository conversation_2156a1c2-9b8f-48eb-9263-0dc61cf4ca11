<template>
  <avue-crud
    :class="className"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    :before-open="beforeOpen"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #body>
      <app-vehicle-audit-dialog
        v-model="auditVisible"
        v-model:form="auditForm"
        :option="auditOption"
        @submit="audited"
        @error="auditError"
      />
    </template>
    <template #auditStatus="{ row }">
      <dict-tag :value="row.auditStatus" :options="yy_audit_status" />
    </template>
    <template #expand="{ row }">
      <app-vehicle-picture v-if="row.id" v-bind="row" wrap />
    </template>
    <template #menu-before="{ row }">
      <el-button icon="check" type="primary" text @click="auditing(row)">
        审核
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { computed, reactive } from 'vue';
import useColumn from './use-column';
import { useDict } from '@/utils/dict';
import { useCrud, useFormDialog } from '@/hooks';

defineOptions({ name: 'Vehicle' });

const props = defineProps({
  driver: Object,
  editable: Boolean,
  other: Object
});

const {
  driver, // 关联的司机
  editable, // 是否可新增编辑删除
  other // 其他选项
} = toRefs(props);

const { yy_audit_status } = useDict('yy_audit_status');

const className = computed(() => {
  return editable.value ? '' : 'app-container';
});

const { column } = useColumn({ editable: editable.value });

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/vehicle',
    defaultSearch: {
      driverId: driver.value?.id
    },
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 1200,
    dialogDrag: true,
    labelWidth: 120,
    expand: true,
    addBtn: editable.value,
    ...other.value
  });

// 审核
const {
  visible: auditVisible,
  form: auditForm,
  option: auditOption,
  open: auditing,
  submit: audited,
  error: auditError
} = useFormDialog({
  url: '/youyang/vehicle/audit',
  column: [
    {
      label: '状态',
      prop: 'status',
      span: 24,
      type: 'radio',
      value: '1',
      rules: [{ required: true, message: '必填的' }],
      border: true,
      dicData: yy_audit_status
    },
    {
      label: '意见',
      prop: 'comment',
      span: 24,
      type: 'textarea'
    }
  ],
  defaultForm: {
    id: undefined,
    status: '1',
    comment: ''
  },
  labelWidth: 80,
  confirm: false,
  after: load
});

// 打开之前赋值关联的司机、车队、
function beforeOpen(done) {
  if (!form.value) {
    form.value = {
      driverId: driver.value?.id,
      driverGroupId: driver.value?.groupId
    };
  } else {
    form.value.driverId = driver.value?.id;
    form.value.driverGroupId = driver.value?.groupId;
  }
  done();
}
</script>

<style lang="scss" scoped></style>
