import { ElMessage } from 'element-plus';
import request from '@/utils/request';
import { useOrderFormColumn, useFormDialog } from '@/hooks';

// 订单表单
export default options => {
  // 表单定义
  const { column } = useOrderFormColumn();

  // 表单对话框
  const { visible, form, option, open, submit, error } = useFormDialog({
    url: '/youyang/order',
    column,
    ...options
  });

  // 匹配客服微信
  async function matchCustomerWechat(phone) {
    const { data } = await request.get('/youyang/passenger/match', {
      params: { phone }
    });
    const { customerWechatIds, customerWechatNames } = data;
    if (!customerWechatIds) {
      return;
    }
    const ids = customerWechatIds.split(',');
    const names = customerWechatNames.split(',');
    if (ids.length > 1) {
      ElMessage.success(`匹配到多个客服微信：${names}。请自行选择`);
      return;
    }
    const [id] = ids;
    const [name] = names;
    form.value.customerWechatId = id;
    ElMessage.success(`匹配到客服微信：${name}`);
  }

  return { visible, form, option, open, submit, error, matchCustomerWechat };
};
