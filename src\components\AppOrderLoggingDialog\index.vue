<template>
  <el-dialog
    title="操作历史"
    :width="860"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @open="open"
    @close="close"
  >
    <avue-crud
      v-model:search="search"
      :table-loading="loading"
      :data="data"
      :option="option"
      @search-change="load"
      @search-reset="load"
      @refresh-change="load"
    >
    </avue-crud>
  </el-dialog>
</template>

<script setup>
import { useCrud } from '@/hooks';

defineOptions({ name: 'AppOrderLoggingDialog', inheritAttrs: false });

const props = defineProps({ orderId: String });

const { orderId } = toRefs(props);

// 大表哥
const { search,  loading, data, option, load } = useCrud({
  name: '操作历史',
  url: '/youyang/orderLog',
  list: '/all',
  column: [
    { label: '操作人', prop: 'createBy', align: 'center', width: 120 },
    { label: '操作时间', prop: 'createTime', align: 'center', width: 180 },
    { label: '操作内容', prop: 'remark' }
  ],
  header: false,
  columnBtn: false,
  gridBtn: false,
  selection: false,
  expand: false,
  menu: false,
  addBtn: false,
  editBtn: false,
  delBtn: false
});

function open() {
  search.value = { orderId: orderId.value };
  load();
}

function close() {
  data.value = [];
}
</script>

<style lang="scss" scoped></style>
