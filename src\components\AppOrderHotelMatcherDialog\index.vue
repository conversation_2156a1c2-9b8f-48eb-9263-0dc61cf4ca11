<template>
  <el-dialog
    title="选择要匹配的地址名"
    :width="800"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
  >
    <el-form :model="model">
      <el-form-item label="开始地址名" prop="startKey">
        <el-radio-group v-model="model.startKey">
          <el-radio :label="form.hotelStartName" value="hotelStartName" />
          <el-radio
            :label="form.hotelStartNameOther"
            value="hotelStartNameOther"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="结束地址名" prop="endKey">
        <el-radio-group v-model="model.endKey">
          <el-radio :label="form.hotelEndName" value="hotelEndName" />
          <el-radio :label="form.hotelEndNameOther" value="hotelEndNameOther" />
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="match"> 匹配 </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';

defineOptions({ name: 'AppOrderHotelMatcherDialog', inheritAttrs: false });

const emit = defineEmits(['update:form', 'match']);

const form = defineModel('form');

const model = ref({
  startKey: 'hotelStartName',
  endKey: 'hotelEndName'
});

function match() {
  emit('match', model.value);
}
</script>

<style lang="scss" scoped></style>
