import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';
import { useDict } from '@/utils/dict';
import { checkRole } from '@/utils/permission';
import useUserStore from '@/store/modules/user';

export default () => {
  const { yy_order_source, yy_order_type, yy_arrived } = useDict(
    'yy_order_source',
    'yy_order_type',
    'yy_arrived'
  );

  const { locale, t } = useI18n();

  const labelType = locale.value == 'th' ? 'thai' : 'name';

  // 用户状态
  const userStore = useUserStore();

  const thai = checkRole(['thai']);

  const admin = checkRole(['admin']);

  // 泰国端显示一些功能
  const thaiTrue = thai && !admin;

  // 泰国端隐藏一些功能
  const thaiFalse = !thaiTrue;

  const column = [
    {
      label: t('order.search.date_index'),
      prop: 'dateIndex',
      width: 60,
      align: 'center',
      search: true,
      searchSpan: 4,
      searchOrder: 4,
      placeholder: '可小写句号分隔'
    },
    {
      label: t('order.search.order_source'),
      prop: 'source',
      width: 70,
      align: 'center',
      search: thaiFalse,
      searchSpan: 4,
      type: 'select',
      dicData: yy_order_source
    },
    {
      label: t('order.search.order_type'),
      prop: 'type',
      width: 60,
      align: 'center',
      search: true,
      searchSpan: 4,
      span: 4,
      type: 'select',
      dicData: yy_order_type
    },
    {
      label: t('order.search.vehicle_time'),
      prop: 'vehicleTimeRange',
      type: 'daterange',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      defaultTime: [
        dayjs().startOf('date').toDate(),
        dayjs().endOf('date').toDate()
      ],
      searchValue: [
        dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')
      ],
      hide: true,
      search: true,
      searchRange: true,
      searchSpan: 8,
      searchOrder: 6
    },
    {
      label: '接车时间',
      prop: 'vehicleTime',
      width: 130,
      align: 'center',
      formatter(row, value) {
        if (value && value != '') {
          return value.slice(0, 16);
        } else {
          return value;
        }
      }
    },
    {
      label: t('order.search.passenger'),
      prop: 'passengerName',
      minWidth: 150,
      align: 'center',
      search: thaiFalse,
      searchSpan: 4,
      placeholder: '可模糊搜索'
    },
    {
      label: t('order.search.passenger_phone'),
      prop: 'passengerPhone',
      width: 150,
      align: 'center',
      hide: thaiTrue,
      search: thaiFalse,
      searchSpan: 4,
      placeholder: '可模糊搜索'
    },
    {
      label: t('order.search.route'),
      prop: 'routeIds',
      width: 80,
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      type: 'select',
      virtualize: true,
      filterable: true,
      multiple: true,
      tags: true,
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/route/all',
      props: { res: 'data', label: 'name', value: 'id' },
      placeholder: '可多选'
    },
    {
      label: t('order.search.route'),
      prop: 'routeName',
      width: 80,
      align: 'center',
      display: true
    },
    {
      label: t('order.search.customer_wechat'),
      prop: 'customerWechat',
      width: thaiFalse ? 140 : 45,
      display: false,
      search: thaiFalse,
      searchSpan: 4
    },
    {
      label: t('order.search.vehicle_group'),
      prop: 'vehicleGroupId',
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      type: 'select',
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/vehicleGroup/all',
      props: { res: 'data', label: 'name', value: 'id' }
    },
    {
      label: t('order.search.driver_phone'),
      prop: 'driverPhone',
      align: 'center',
      width: 100,
      hide: thaiFalse,
      search: true,
      searchSpan: 4,
      placeholder: '可模糊搜索'
    },
    {
      label: t('order.search.vehicle_number'),
      prop: 'vehicleNumber',
      align: 'center',
      width: 100,
      hide: thaiFalse,
      search: true,
      searchSpan: 4
    },
    {
      label: t('order.search.order_vehicle_type'),
      prop: 'orderVehicleTypeIds',
      width: 100,
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      type: 'select',
      filterable: true,
      multiple: true,
      tags: true,
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/vehicleType/all',
      props: { res: 'data', label: labelType, value: 'id' },
      placeholder: '可多选'
    },
    {
      label: '订单车型',
      prop: 'orderVehicleTypeName',
      width: 100,
      align: 'center'
    },
    {
      label: t('order.search.arrived'),
      prop: 'arrived',
      width: 40,
      align: 'center',
      search: thaiFalse,
      searchSpan: 4,
      type: 'select',
      dicData: yy_arrived
    },
    {
      label: '电话图标',
      prop: 'passengerPhoneColor',
      width: 40,
      align: 'center'
    },
    {
      label: 'IM图标',
      prop: 'passengerImColor',
      width: 40,
      align: 'center'
    },
    {
      label: t('order.search.owner_by'),
      prop: 'ownerBy',
      align: 'center',
      width: 65,
      hide: thaiTrue,
      search: thaiFalse,
      searchSpan: 4
    },
    {
      label: t('order.search.flight'),
      prop: 'flight',
      width: 65,
      align: 'center',
      search: true,
      searchSpan: 4
    },
    {
      label: '机场',
      prop: 'airport',
      width: 45,
      align: 'center'
    },
    {
      label: '出站口',
      prop: 'exitPort',
      width: 30,
      align: 'center'
    },
    {
      label: t('order.search.group'),
      prop: 'groupId',
      align: 'center',
      hide: true,
      search: thaiFalse,
      searchSpan: 4,
      searchOrder: 3,
      type: 'select',
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/orderGroup/all',
      props: { res: 'data', label: 'name', value: 'id' }
    },
    {
      label: t('order.search.index'),
      prop: 'index',
      width: 40,
      align: 'center',
      search: thaiFalse,
      searchSpan: 4,
      searchOrder: 2,
      placeholder: '可小写句号分隔'
    },
    {
      label: t('order.search.sale'),
      prop: 'sale',
      width: 160,
      align: 'center',
      search: true,
      searchSpan: 4,
      searchOrder: 5,
      placeholder: '可模糊搜索'
    },
    {
      label: '同步图标',
      prop: 'syncColor',
      width: 40,
      align: 'center'
    },
    {
      label: '回访图标',
      prop: 'visitColor',
      width: 40,
      align: 'center'
    },

    {
      label: t('order.search.product_name'),
      prop: 'productName',
      align: 'center',
      hide: true,
      search: thaiFalse,
      searchSpan: 4
    },
    {
      label: t('order.search.product_package'),
      prop: 'productPackage',
      align: 'center',
      hide: true,
      search: thaiFalse,
      searchSpan: 4
    },
    {
      label: t('order.search.sort'),
      prop: 'sort',
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      type: 'select',
      dicData: [
        { label: t('order.sort.vehicle_time_asc'), value: 0 },
        { label: t('order.sort.vehicle_time_desc'), value: 1 },
        { label: t('order.sort.dateIndex_asc'), value: 2 },
        { label: t('order.sort.dateIndex_desc'), value: 3 }
      ]
    }
  ];

  return { column };
};
