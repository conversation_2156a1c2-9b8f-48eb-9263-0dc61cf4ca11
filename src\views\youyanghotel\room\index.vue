<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    :before-open="beforeOpen"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  />
</template>

<script setup>
import { useCrud } from '@/hooks';

const props = defineProps({
  room: Object
});

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyanghotel/room',
    defaultSearch: {
      hotelId: props.room?.id
    },
    column: [
      {
        label: '房型名称',
        prop: 'name',
        headerAlign: 'center',
        minWidth: 240,
        display: true,
        search: true,
        searchSpan: 6,
        span: 24,
        rules: [{ required: true, message: '必填的' }]
      },
      {
        label: '面积',
        prop: 'area',
        align: 'center',
        width: 180,
        display: true,
        search: true,
        searchSpan: 6,
        span: 24,
        rules: [{ required: true, message: '必填的' }]
      },
      {
        label: '楼层',
        prop: 'floor',
        align: 'center',
        width: 180,
        display: true,
        search: true,
        searchSpan: 6,
        span: 24
      }
    ],
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    labelWidth: 120,
    searchSpan: 6,
    searchLabelWidth: 80
  });

function beforeOpen(done) {
  if (!form.value) {
    form.value = {
      hotelId: props.room?.id
    };
  } else {
    form.value.hotelId = props.room?.id;
  }
  done();
}
</script>

<style lang="scss" scoped></style>
