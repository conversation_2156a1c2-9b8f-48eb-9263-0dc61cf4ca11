import { reactive } from 'vue';
import request from '@/utils/request';

export default () => {
  const stats = reactive({
    total: 0,
    completed: 0,
    cancelled: 0,
    remaining: 0
  });

  async function getStats() {
    const { data } = await request.get('/youyang/order/stats');
    if (!data) {
      return;
    }
    stats.total = data.total;
    stats.completed = data.completed;
    stats.cancelled = data.cancelled;
    stats.remaining = data.remaining;
  }

  return { stats, getStats };
};
