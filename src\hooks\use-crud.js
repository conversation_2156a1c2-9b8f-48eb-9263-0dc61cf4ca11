import { computed, ref, unref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import differenceBy from 'lodash/differenceBy';
import pullAllBy from 'lodash/pullAllBy';
import request, { download as requrestDownload } from '@/utils/request';

// CRUD功能
export default options => {
  // 解构选项
  const {
    // 基本url
    url,
    // list路由后缀
    list = '/list',
    // 表单的默认值，只能首次使用
    defaultForm,
    // 搜索的默认值，只能首次使用
    defaultSearch,
    // 分页的默认值
    defaultPage,
    // 加载前回调
    before,
    // 加载后回调
    after,
    // 响应结果映射回调
    mapper,
    // 新增之前回调
    beforeAdd,
    // 新增之后回调
    afterAdd,
    // 新增之后加载列表
    loadAdd = true,
    // 编辑之前回调
    beforeEdit,
    // 编辑之后回调
    afterEdit,
    // 编辑之后加载列表
    loadEdit = true,
    // 删除之前回调
    beforeDel,
    // 删除之后回调
    afterDel,
    // 删除之后加载列表
    loadDel = true,
    // 勾选之前
    selectBefore,
    // 勾选之后
    selectAfter,
    // 下载之前
    downloadBefore,
    // 下载之后
    downloadAfter
  } = options;

  // 表单
  const form = ref(defaultForm);

  // 搜索值
  const search = ref(defaultSearch);

  // 选中的项
  const selected = ref([]);

  // 能选择
  const canSelect = computed(() => {
    return selected.value.length > 0;
  });

  // 分页
  const page = ref({
    currentPage: 1,
    pageSize: 10,
    total: 0,
    pagerCount: 7,
    background: true,
    layout: 'total, sizes, prev, pager, next, jumper',
    pageSizes: [10, 20, 30, 40, 50, 100],
    single: false,
    ...defaultPage
  });

  // 加载中
  const loading = ref(false);

  // 表格数据
  const data = ref([]);

  // 表格选项
  const option = ref({
    border: true,
    stripe: true,
    searchSpan: 4,
    expandRowKeys: [],
    ...options
  });

  // 加载、搜索、重置
  async function load(_, done) {
    try {
      loading.value = true;
      const { currentPage, pageSize } = unref(page);
      let params = { currentPage, pageSize, ...unref(search) };
      // 请求之前回调
      if (before && typeof before == 'function') {
        params = await before(params);
      }
      // list的url
      const _url = list && list != '' ? `${url}${list}` : url;
      const result = await request.get(_url, {
        params
      });
      if (result.code != 200) {
        throw new Error(result.msg);
      }
      if (Array.isArray(result.data)) {
        // 不分页
        data.value = result.data;
      } else {
        // 返回数据变换
        if (mapper && typeof mapper == 'function') {
          result.rows = await mapper(result.rows);
        }
        data.value = result.rows;
        // 总数量
        page.value.total = result.total;
      }
      // 请求之后回调
      if (after && typeof after == 'function') {
        await after(params, data.value);
      }
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
      // 完成
      if (done && typeof done == 'function') {
        done();
      }
    }
  }

  // 新增
  async function add(_form, done, loading) {
    try {
      // 新增之前回调
      if (beforeAdd && typeof beforeAdd == 'function') {
        _form = await beforeAdd(_form);
      }
      const { data: row, msg, code } = await request.post(url, _form);
      if (code != 200) {
        throw new Error(msg);
      }
      // 新增之后回调
      if (afterAdd && typeof afterAdd == 'function') {
        await afterAdd(row);
      }
      // 新增之后加载列表
      if (loadAdd) {
        await load();
      }
      done();
      ElMessage.success(msg);
    } catch (error) {
      console.error(error);
    } finally {
      loading();
    }
  }

  // 编辑
  async function edit(_form, index, done, loading) {
    try {
      // 编辑之前回调
      if (beforeEdit && typeof beforeEdit == 'function') {
        _form = await beforeEdit(_form, index);
      }
      const { data: row, msg, code } = await request.put(url, _form);
      if (code != 200) {
        throw new Error(msg);
      }
      // 编辑之后回调
      if (afterEdit && typeof afterEdit == 'function') {
        await afterEdit(row, index);
      }
      // 编辑之后加载列表
      if (loadEdit) {
        await load();
      }
      done();
      ElMessage.success(msg);
    } catch (error) {
      console.error(error);
    } finally {
      loading();
    }
  }

  // 删除
  async function del(_form, index, done) {
    try {
      await ElMessageBox.confirm('确定要删除吗?', '提示', {
        type: 'warning'
      });
      // 删除之前回调
      if (beforeDel && typeof beforeDel == 'function') {
        _form = await beforeDel(_form, index);
      }
      const { msg, code } = await request.delete(`${url}/${_form.id}`);
      if (code != 200) {
        throw new Error(msg);
      }
      // 删除之后回调
      if (afterDel && typeof afterDel == 'function') {
        await afterDel(row, index);
      }
      // 删除之后加载列表
      if (loadDel) {
        await load();
      }
      done();
      ElMessage.success(msg);
    } catch (error) {
      if (error != 'cancel') {
        console.error(error.message);
      }
    }
  }

  // 导出
  async function download(otherParams, name) {
    try {
      const ids = selected.value.map(item => item.id);
      let params = { ...unref(search), ids, ...otherParams };
      if (downloadBefore && typeof downloadBefore == 'function') {
        params = await downloadBefore(params);
      }
      const response = await requrestDownload(
        `${url}/export`,
        params,
        `${name}_${new Date().getTime()}.xlsx`,
        {
          method: 'post',
          timeout: 60 * 1000
        }
      );
      if (response?.code && response.code != 200) {
        throw new Error(msg);
      }
      if (downloadAfter && typeof downloadAfter == 'function') {
        await downloadAfter(e);
      }
      ElMessage.success('导出成功');
    } catch (error) {
      console.error(error);
    }
  }

  // 展开单条
  function expandOne(row, expandedRows) {
    if (Array.isArray(expandedRows) && expandedRows.length > 0) {
      option.value.expandRowKeys = [row.id];
    } else {
      option.value.expandRowKeys = [];
    }
  }

  // 展开单条，在表格行上操作
  function expandOneRow(row) {
    const has = option.value.expandRowKeys.some(item => item == row.id);
    const expandedRows = has ? undefined : [row];
    expandOne(row, expandedRows);
  }

  // 行展开
  function expand(row) {
    const index = option.value.expandRowKeys.findIndex(item => item == row.id);
    if (index != -1) {
      option.value.expandRowKeys.splice(index, 1);
    } else {
      option.value.expandRowKeys.push(row.id);
    }
  }

  // 如果未展开则展开第一行
  function expandFirst() {
    if (data.value.length == 0) {
      return;
    }
    if (option.value.expandRowKeys.length > 0) {
      return;
    }
    const [first] = data.value;
    option.value.expandRowKeys = [first.id];
  }

  // 选中
  async function select(rows) {
    // 选中之前回调
    if (selectBefore && typeof selectBefore == 'function') {
      await selectBefore(rows);
    }
    selected.value = rows;
    // 选中之后回调
    if (selectAfter && typeof selectAfter == 'function') {
      await selectAfter(rows);
    }
  }

  // 同步数据，多出来的删除，少的增加
  function sync(rows, key = 'id') {
    // 找出需要删除的元素
    const removed = differenceBy(data.value, rows, key);
    // 找出需要添加的元素
    const added = differenceBy(rows, data.value, key);
    // 删除多余元素
    pullAllBy(data.value, removed, key);
    // 添加缺少元素
    data.value.push(...added);
  }

  // 同步某个属性
  function syncBy(rows, prop, key = 'id') {
    data.value.forEach(item => {
      const value = rows.find(row => row[key] == item[key]);
      if (value) {
        item[prop] = value[prop];
      }
    });
  }

  return {
    form,
    search,
    selected,
    canSelect,
    page,
    loading,
    data,
    option,
    load,
    add,
    edit,
    del,
    download,
    expandOne,
    expandOneRow,
    expand,
    expandFirst,
    select,
    sync,
    syncBy
  };
};
