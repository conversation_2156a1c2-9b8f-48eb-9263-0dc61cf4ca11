import { useDict } from '@/utils/dict';
import useUserStore from '@/store/modules/user';

export default () => {
  const { yy_order_source, yy_order_channel, yy_order_type } = useDict(
    'yy_order_source',
    'yy_order_channel',
    'yy_order_type'
  );

  const userStore = useUserStore();

  const column = [
    {
      label: '销售订单号',
      prop: 'sale',
      span: 6,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '订单类型',
      prop: 'type',
      span: 6,
      type: 'select',
      dicData: yy_order_type,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '渠道来源',
      prop: 'channelSource',
      span: 6,
      type: 'select',
      dicData: yy_order_channel
    },
    {
      label: '订单来源',
      prop: 'source',
      span: 6,
      type: 'select',
      dicData: yy_order_source,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '抵达航班',
      prop: 'flight',
      span: 6
    },
    {
      label: '机场',
      prop: 'airport',
      span: 6
    },
    {
      label: '订单车型',
      prop: 'orderVehicleTypeId',
      span: 6,
      type: 'select',
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/vehicleType/all',
      props: { res: 'data', label: 'name', value: 'id' },
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '路线',
      prop: 'routeId',
      span: 6,
      type: 'select',
      disabled: false,
      virtualize: true,
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/route/all',
      props: { res: 'data', label: 'name', value: 'id' },
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '接车时间',
      prop: 'vehicleTime',
      span: 6,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '订单金额',
      prop: 'price',
      span: 6,
      type: 'number',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '结算金额',
      prop: 'fee',
      span: 6,
      type: 'number'
    },
    {
      label: '增值服务费',
      prop: 'vas',
      span: 6,
      type: 'number'
    },
    {
      label: '里程',
      prop: 'km',
      span: 6,
      type: 'number',
      row: true,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '起点名称',
      prop: 'hotelStartName',
      span: 12,
      rules: [{ required: true, message: '必填的' }]
    },

    {
      label: '终点名称',
      prop: 'hotelEndName',
      span: 12,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '起点名称2',
      prop: 'hotelStartNameOther',
      span: 12
    },
    {
      label: '终点名称2',
      prop: 'hotelEndNameOther',
      span: 12
    },
    {
      label: '起点地址',
      prop: 'hotelStartAddress',
      span: 12,
      type: 'textarea',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '终点地址',
      prop: 'hotelEndAddress',
      span: 12,
      type: 'textarea',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '',
      prop: 'match',
      span: 24
    },
    {
      label: '乘客姓名',
      prop: 'passengerName',
      span: 6,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '乘客拼音',
      prop: 'passengerPinyin',
      span: 6,
      placeholder: '留空，提交后生成'
    },
    {
      label: '乘客生日',
      prop: 'passengerBrithdate',
      span: 6
    },
    {
      label: '乘客护照',
      prop: 'passengerPassport',
      span: 6
    },
    {
      label: '成人数',
      prop: 'number',
      type: 'number',
      span: 6,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '儿童数',
      prop: 'childrenNumber',
      type: 'number',
      span: 6
    },
    {
      label: '行李数',
      prop: 'baggageNumber',
      type: 'number',
      span: 6
    },
    {
      label: '乘客邮箱',
      prop: 'passengerEmail',
      span: 6
    },
    {
      label: '乘客电话',
      prop: 'passengerPhone',
      span: 6
    },
    {
      label: '乘客微信',
      prop: 'passengerWechat',
      span: 6
    },
    {
      label: '乘客备注',
      prop: 'passengerRemark',
      span: 6
    },
    {
      label: '客服微信',
      prop: 'customerWechatId',
      span: 6,
      type: 'select',
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/orderWechat/all',
      props: { res: 'data', label: 'name', value: 'id' },
      tip: '可在订单配置-->客服微信内增加'
    },
    {
      label: '产品名称',
      prop: 'productName',
      span: 6
    },
    {
      label: '套餐名称',
      prop: 'productPackage',
      span: 6
    },
    {
      label: '集合地点',
      prop: 'productAssemblingPlace',
      span: 6
    },
    {
      label: '集合时间',
      prop: 'productAssemblingTime',
      span: 6,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      label: '重要提醒',
      prop: 'productWarning',
      span: 6
    },
    {
      label: '注意事项',
      prop: 'productNote',
      span: 6
    }
  ];

  return {
    column
  };
};
