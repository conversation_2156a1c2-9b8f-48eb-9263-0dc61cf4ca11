<template>
  <div v-loading="loading" class="ticket-page">
    <div class="ticket-page__header">
      <el-button class="ticket-page__print" type="primary" @click="previewing">
        打印
      </el-button>
    </div>
    <div class="ticket-page__body">
      <app-order-ticket v-bind="data" />
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { useDetails } from '@/hooks';

// 当前路由
const route = useRoute();

const { id } = route.query;

const { loading, data, load } = useDetails({
  id,
  url: `youyang/order`
});

function previewing() {
  print();
}

load();
</script>

<style lang="scss" scoped>
.ticket-page {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px;

    @media print {
      display: none;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
  }

  &__print {
    width: 100px;
    height: 40px;
    font-size: 20px;
  }
}
</style>
