import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const column = [
    {
      label: '车队',
      prop: 'driverGroupName',
      align: 'center'
    },
    {
      label: '司机电话',
      prop: 'driverPhone',
      align: 'center',
      search: true,
      searchSpan: 6
    },
    {
      label: '车牌',
      prop: 'number',
      align: 'center',
      search: true,
      searchSpan: 6
    },
    {
      label: '车型',
      prop: 'typeId',
      align: 'center',
      type: 'select',
      search: true,
      searchSpan: 6,
      type: 'select',
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/vehicleType/all',
      props: { res: 'data', label: 'name', value: 'id' }
    }
  ];
  return { column };
};
