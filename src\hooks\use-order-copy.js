import useClipboard from 'vue-clipboard3';
import { ElMessage } from 'element-plus';

export default () => {
  // 剪切板操作
  const { toClipboard } = useClipboard();

  // 复制
  async function copy(data) {
    try {
      // 展示方式：
      // NO206 13:10 สขว-แอสุ //5V//
      // ...
      const text = data
        .map(item => {
          let {
            dateIndex,
            vehicleTime,
            hotelStartShortNameThai,
            hotelEndShortNameThai,
            orderVehicleTypeNameThai
          } = item;
          hotelStartShortNameThai = hotelStartShortNameThai ?? '';
          hotelEndShortNameThai = hotelEndShortNameThai ?? '';
          orderVehicleTypeNameThai = orderVehicleTypeNameThai ?? '';
          const hhmm = vehicleTime.slice(10, 16);
          return `NO${dateIndex} ${hhmm} ${hotelStartShortNameThai} - ${hotelEndShortNameThai} //${orderVehicleTypeNameThai}//`;
        })
        .join('\n');
      await toClipboard(text);
      ElMessage.success('复制成功');
    } catch (e) {
      ElMessage.error(e.message);
    }
  }

  return { copy };
};
