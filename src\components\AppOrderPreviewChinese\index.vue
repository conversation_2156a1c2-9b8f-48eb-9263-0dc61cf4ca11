<template>
  <div class="app-order-preview-chinese">
    <div class="app-order-preview-chinese__header">
      <div class="app-order-preview-chinese__index">{{ dateIndex }}</div>
      <div class="app-order-preview-chinese__time">
        {{ vehicleTime?.slice(0, 16) }}
      </div>
    </div>
    <div
      class="app-order-preview-chinese__body"
      :style="{ fontSize: fontSize + 'px' }"
    >
      {{ passengerName }}
    </div>
    <div class="app-order-preview-chinese__footer">
      <div>{{ orderVehicleTypeName }}</div>
      <div>
        <div>{{ flight }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'AppOrderPreviewChinese',
  inheritAttrs: false
});

const props = defineProps({
  vehicleTime: String,
  routeName: String,
  flight: String,
  passengerName: String,
  orderVehicleTypeName: String,
  groupName: String,
  index: Number,
  dateIndex: Number
});

const { passengerName } = toRefs(props);
// 计算字体大小（基于字符长度）
const fontSize = computed(() => {
  const len = passengerName.value.length;
  if (len <= 6) {
    return 242;
  } else if (len <= 12) {
    return 120;
  } else if (len <= 18) {
    return 80;
  } else if (len <= 24) {
    return 60;
  } else if (len <= 30) {
    return 48;
  } else if (len <= 36) {
    return 40;
  } else {
    return 44;
  }
});
</script>

<style lang="scss" scoped>
.app-order-preview-chinese {
  display: flex;
  flex-direction: column;
  width: 29cm;
  height: 10.5cm;
  border-bottom: thin dashed var(--el-border-color);
  font-family: 微软雅黑;

  &:nth-child(2n) {
    page-break-after: always;
  }

  &:last-child {
    page-break-after: auto;
  }

  &__header {
    position: relative;
    display: flex;
    justify-content: space-between;
    font-size: 24px;
  }

  &__index {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 48px;
  }

  &__time {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 48px;
  }

  &__body {
    flex: 1 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    word-break: break-all;
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    font-size: 24px;
  }
}
</style>
