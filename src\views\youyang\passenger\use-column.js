import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const column = [
    {
      label: '姓名',
      prop: 'name',
      headerAlign: 'center',
      rules: [{ required: true, message: '必填的' }],
      search: true,
      searchSpan: 4,
      span: 24
    },
    {
      label: '拼音',
      prop: 'pinyin',
      headerAlign: 'center',
      span: 24
    },
    {
      label: '电话',
      prop: 'phone',
      width: 240,
      align: 'center',
      span: 24
    },
    {
      label: '客服微信号',
      prop: 'customerWechatIds',
      headerAlign: 'center',
      span: 24,
      type: 'select',
      multiple: true,
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/orderWechat/all',
      props: { res: 'data', label: 'name', value: 'id' }
    }
  ];

  return { column };
};
