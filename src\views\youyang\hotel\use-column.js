import { useDict } from '@/utils/dict';

export default () => {
  const { yy_level } = useDict('yy_level');

  const column = [
    {
      label: '详细信息',
      prop: 'info',
      headerAlign: 'center',
      display: false
    },
    {
      label: '名称',
      prop: 'name',
      minWidth: 480,
      headerAlign: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      placeholder: '可模糊搜索',
      span: 24,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '简称',
      prop: 'shortName',
      minWidth: 120,
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      placeholder: '可模糊搜索',
      rules: [{ required: true, message: '必填的' }],
      span: 12
    },
    {
      label: '简称(Thai)',
      prop: 'shortNameThai',
      width: 120,
      align: 'center',
      hide: true,
      span: 12
    },

    {
      label: '详细地址',
      prop: 'address',
      minWidth: 800,
      headerAlign: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      placeholder: '可模糊搜索',
      span: 24,
      type: 'textarea',
      rows: 12,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '星级',
      prop: 'level',
      width: 100,
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 4,
      type: 'select',
      dicData: yy_level,
      span: 12,
      row: true
    },
    {
      label: '经纬度',
      prop: 'lonLat',
      width: 180,
      align: 'center',
      hide: true,
      span: 24
    }
  ];

  return { column };
};
