<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  />
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';

defineOptions({ name: 'ConfGroup' });

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/passenger',
    column,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    beforeAdd: setParams,
    beforeEdit: setParams
  });

// 数组参数逗号分隔
function setParams(_form) {
  const { customerWechatIds } = _form;
  if (Array.isArray(customerWechatIds)) {
    _form.customerWechatIds = customerWechatIds.join(',');
  }
  return _form;
}
</script>

<style lang="scss" scoped></style>
