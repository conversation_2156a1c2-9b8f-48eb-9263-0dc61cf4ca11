<template>
  <div v-if="canShowcard" class="app-container">
    <div class="thai-page">
      <el-link type="primary" href="order/showcard" target="_balck">
        举牌查询
      </el-link>
      <el-divider />
      <div>
        <el-date-picker
          v-model="value"
          type="datetimerange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm:ss"
          :default-value="defautltValue"
          :default-time="defaultTime"
          :shortcuts="shortcuts"
          :disabled-date="disabledDate"
          :clearable="false"
          :editable="false"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          @change="change"
        />

        <el-button
          class="thai-page__btn"
          type="primary"
          @click="download({ exportType: 'thaiShowcard' }, '用车订单_举牌')"
        >
          举牌导出
        </el-button>
        <el-button :loading="loading" type="primary" @click="previewThai">
          举牌打印
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs';
import { useOrderShowcard, useOrderPreview } from '@/hooks';

// 开发模式
const dev = import.meta.env.DEV;

// 开发模式指定日期
const devDateTime = [
  dayjs('2025-08-19 00:00:00').format(formatTempalte),
  dayjs('2025-08-19 23:59:59').format(formatTempalte)
];

// 格式化模板
const formatTempalte = 'YYYY-MM-DD HH:mm:ss';

// 默认日期
const defautltValue = [dayjs().startOf('date'), dayjs().endOf('date')];

// 默认时间
const defaultTime = [dayjs().startOf('date'), dayjs().endOf('date')];

// 昨天
const yesterday = [
  dayjs().subtract(1, 'd').startOf('date').format(formatTempalte),
  dayjs().subtract(1, 'd').endOf('date').format(formatTempalte)
];

// 今天
const today = [
  dayjs().startOf('date').format(formatTempalte),
  dayjs().endOf('date').format(formatTempalte)
];

// 明天
const tomorrow = [
  dayjs().add(1, 'd').startOf('date').format(formatTempalte),
  dayjs().add(1, 'd').endOf('date').format(formatTempalte)
];

// 快捷选择日期
const shortcuts = [
  { text: '昨天', value: () => yesterday },
  { text: '今天', value: () => today },
  { text: '明天', value: () => tomorrow }
];

// 禁用不可选择的日期
function disabledDate(date) {
  // 开发模式可以选拔任何时间
  if (dev) {
    return false;
  }
  const start = dayjs().subtract(2, 'd');
  const end = dayjs().add(1, 'd');
  const range = dayjs(date);
  const before = start > range;
  const after = range > end;
  const disabled = before || after;
  return disabled;
}

// 绑定的值
const value = ref(dev ? devDateTime : today);

// 举牌查询，AA、BB、CC、FON账号
const { canShowcard, loading, search, data, download, load } = useOrderShowcard(
  {
    url: '/youyang/order',
    list: '/all',
    defaultSearch: {
      statuses: '1,3,60',
      startVehicleTime: dev ? devDateTime[0] : today[0],
      endVehicleTime: dev ? devDateTime[1] : today[1]
    }
  }
);

// 举牌打印，AA、BB、CC、FON账号
const { previewed } = useOrderPreview();

// 日期变更
function change(e) {
  const [start, end] = e;
  search.value.startVehicleTime = start;
  search.value.endVehicleTime = end;
}

// 泰国端
async function previewThai() {
  await load();
  previewed(data.value, true);
}
</script>

<style lang="scss" scoped>
.thai-page {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  &__btn {
    margin-left: 12px;

    @media (max-width: 640px) {
      margin-left: 0;
      margin-top: 12px;
    }
  }
}
</style>
