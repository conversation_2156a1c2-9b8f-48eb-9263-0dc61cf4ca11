<template>
  <el-dialog
    title="修改订单"
    top="0"
    :width="1600"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
  >
    <app-order-form
      v-model="form"
      :option="option"
      :loading="loading"
      @submit="submit"
      @error="error"
      @generate="generate"
      @match="match"
      @passenger-phone-blur="passengerPhoneBlur"
    />
  </el-dialog>
</template>

<script setup>
defineOptions({ name: 'AppOrderFormDialog', inheritAttrs: false });

const props = defineProps({
  option: Object,
  loading: Boolean
});

const emit = defineEmits([
  'update:form',
  'submit',
  'error',
  'match',
  'passenger-phone-blur'
]);

const form = defineModel('form');

// 仅转发事件
function submit(form, done) {
  emit('submit', form, done);
}

// 仅转发事件
function error(error) {
  emit('error', error);
}

// 仅转发事件
function generate(form) {
  emit('generate', form);
}

// 仅转发事件
function match(form) {
  emit('match', form);
}
// 仅转发事件
function passengerPhoneBlur(form) {
  emit('passenger-phone-blur', form);
}
</script>

<style lang="scss" scoped></style>
