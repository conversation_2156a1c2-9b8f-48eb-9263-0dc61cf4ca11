import { useDict } from '@/utils/dict';
import useUserStore from '@/store/modules/user';

export default () => {
  const { yy_enable } = useDict('yy_sex', 'yy_enable');

  // 用户状态
  const user = useUserStore();

  const column = [
    {
      label: '所属车队',
      prop: 'groupId',
      width: 240,
      align: 'center',
      search: true,
      searchSpan: 4,
      span: 24,
      type: 'select',
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/vehicleGroup/all',
      props: { res: 'data', label: 'name', value: 'id' },
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '电话',
      prop: 'phone',
      width: 240,
      align: 'center',
      search: true,
      searchSpan: 4,
      placeholder: '可模糊搜索',
      span: 24,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '车牌',
      prop: 'numbers',
      minWidth: 380,
      headerAlign: 'center',
      display: false
    },
    {
      label: '车牌',
      prop: 'number',
      hide: true,
      display: false,
      search: true,
      searchSpan: 4,
      placeholder: '可模糊搜索'
    },
    {
      label: '状态',
      prop: 'status',
      width: 180,
      align: 'center',
      type: 'switch',
      search: true,
      searchSpan: 4,
      value: '1',
      dicData: yy_enable
    }
  ];

  return { column };
};
