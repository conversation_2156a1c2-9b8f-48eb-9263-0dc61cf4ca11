<template>
  <div class="app-preview">
    <div class="app-preview__header">
      <div class="app-preview__route">{{ routeName }}</div>
      <div class="app-preview__type">
        {{ orderVehicleTypeName }}
      </div>
      <div class="app-preview__remark">{{ passengerRemark }}</div>
      <div class="app-preview__vehicle-time">
        {{ vehicleTime?.slice(0, 16) }}
      </div>
      <div class="app-preview__flight">{{ flight }}</div>
      <div class="app-preview__airport">{{ airport }}</div>
      <div class="app-preview__sale">{{ sale }}</div>
    </div>
    <div class="app-preview__body">
      <div class="app-preview__content">
        <div class="app-preview__fullname">
          <div class="app-preview__name" :style="{ fontSize: fontSize + 'px' }">
            {{ passengerName }}
          </div>
          <div
            v-if="hidePinyin"
            class="app-preview__pinyin"
            :style="{ fontSize: 50 + 'px' }"
          >
            {{ passengerPinyin }}
          </div>
        </div>
        <el-image
          v-viewer
          v-if="showPicture"
          fit="contain"
          class="app-preview__vehicle"
          :src="peoplePicture"
        />
      </div>
      <div class="app-preview__number">
        Adult:
        <span class="app-preview__adult"> {{ number }} </span>
        Child:
        <span class="app-preview__children">{{ childrenNumber }}</span>
      </div>
      <div class="app-preview__start">
        <div class="app-preview__label">Start:</div>
        <div>
          {{ hotelStartAddress }}
        </div>
      </div>
      <div class="app-preview__end">
        <div class="app-preview__end app-preview__end--left">
          <div class="app-preview__label">
            <div>End:</div>
            <div class="app-preview__km">{{ km }}KM</div>
            <div class="app-preview__group">{{ groupName }}{{ index }}</div>
          </div>
          <div>
            {{ hotelEndAddress }}
          </div>
        </div>
        <div class="app-preview__end app-preview__end--right">
          <div class="app-preview__index">{{ dateIndex }}</div>
          <div class="app-preview__type-thai">
            {{ orderVehicleTypeNameThai }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="showPicture" class="app-preview__footer">
      <div class="app-preview__driver-phone">
        เบอร์โทรคนขับ：{{ driverPhone }}
      </div>
      <div class="app-preview__vehicle-number">
        ป้ายทะเบียน：{{ vehicleNumber }}
      </div>
    </div>
    <div v-if="cancel" class="app-preview__cancel">Cancel</div>
  </div>
</template>

<script setup>
import { computed, toRefs, watch } from 'vue';
import request from '@/utils/request';

defineOptions({ name: 'AppOrderPreview', inheritAttrs: false });

const props = defineProps({
  showPicture: Boolean,
  routeName: String,
  orderVehicleTypeName: String,
  orderVehicleTypeNameThai: String,
  passengerRemark: String,
  vehicleTime: String,
  flight: String,
  airport: String,
  sale: String,
  passengerName: String,
  passengerPinyin: String,
  number: Number,
  childrenNumber: Number,
  hotelStartAddress: String,
  hotelEndAddress: String,
  km: Number,
  groupName: String,
  index: Number,
  dateIndex: Number,
  driverPhone: String,
  vehicleId: String,
  vehicleNumber: String,
  status: String
});

const { showPicture, passengerName, vehicleId, status } = toRefs(props);

// 是否显示拼音
const hidePinyin = computed(() => {
  const firstChar = passengerName.value.charAt(0);
  if (
    (firstChar >= 'A' && firstChar <= 'Z') ||
    (firstChar >= 'a' && firstChar <= 'z')
  ) {
    return false;
  } else {
    return true;
  }
});

// 计算字体大小（基于字符长度）
const fontSize = computed(() => {
  const len = passengerName.value.length;
  if (len <= 6) {
    return 100;
  } else if (len <= 12) {
    return 80;
  } else if (len <= 18) {
    return 60;
  } else if (len <= 24) {
    return 48;
  } else if (len <= 30) {
    return 40;
  } else if (len <= 36) {
    return 32;
  } else {
    return 30;
  }
});

// 显示取消字样
const cancel = computed(() => {
  return status.value == '4';
});

// 人车合照
const peoplePicture = ref();

// 开发版连到正式版链接
const dev = import.meta.env.DEV;
const host = dev ? 'http://************' : '';

// 获取人车合照
watch(
  [() => showPicture.value, () => vehicleId.value],
  async ([_showPicture, _vehcile]) => {
    if (_showPicture && _vehcile && _vehcile != '') {
      const { data } = await request.get(`/youyang/vehicle/${_vehcile}`);
      peoplePicture.value = `${host}/api${data.peoplePicture}`;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.app-preview {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 24cm;
  min-height: 12cm;
  border-bottom: thin dashed var(--el-border-color);
  margin-top: 30px;
  padding-bottom: 20px;
  font-weight: bold;
  font-family: 微软雅黑;
  color: black;
  page-break-after: always;

  @media (max-width: 800px) {
    width: 18cm;
  }

  &:last-child {
    page-break-after: auto;
  }

  &__header {
    display: flex;
    align-items: flex-start;
    font-size: 18px;
  }

  &__route {
    flex: 3 0 auto;
    padding-right: 20px;
  }

  &__type {
    flex: 4 0 auto;
    padding-right: 20px;
  }

  &__remark {
    flex: 2 0 auto;
    padding-right: 20px;
  }

  &__vehicle-time {
    flex: 8 0 auto;
    display: flex;
    justify-content: center;
    color: red;
    padding-right: 20px;
  }

  &__flight {
    flex: 0 1 80px;
    display: flex;
    justify-content: center;
    background-color: red;
    color: white;
  }

  &__airport {
    flex: 1 0 auto;
    padding-left: 20px;
    padding-right: 20px;
  }

  &__sale {
    flex: 6 0 auto;
    padding-left: 20px;
  }

  &__body {
    flex: 1 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__content {
    display: flex;
    align-items: flex-end;
  }

  &__fullname {
    flex: 1 0;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    margin: 30px 0;
    min-height: 200px;
  }

  &__name {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    font-size: 100px;
    word-break: break-all;
  }

  &__pinyin {
    display: flex;
    justify-content: center;
    height: 85px;
    font-size: 44px;

    &--long {
      height: 59px;
      font-size: 34px;
    }
  }

  &__vehicle {
    flex: 0 0 240px;
    width: 180px;
    height: 180px;
  }

  &__number {
    display: flex;
    justify-content: center;
    color: var(--el-text-color-placeholder);
    font-size: 18px;
    font-weight: normal;
  }

  &__adult,
  &__children {
    margin-left: 6px;
    margin-right: 12px;
    color: red;
  }

  &__start {
    display: flex;
    margin-top: 20px;
    font-size: 22px;
  }

  &__end {
    display: flex;
    margin-top: 20px;
    font-size: 22px;

    &--left {
      flex: 1 0;
    }

    &--right {
      flex: 0 0 auto;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      padding-left: 20px;
    }
  }

  &__label {
    flex: 0 0 100px;
  }

  &__km {
    margin-top: 12px;
    color: var(--el-text-color-placeholder);
    font-size: 16px;
    font-weight: normal;
  }

  &__index {
    font-size: 36px;
    font-weight: normal;
  }

  &__type-thai {
    color: red;
  }

  &__group {
    color: var(--el-text-color-placeholder);
    font-size: 16px;
    font-weight: normal;
  }

  &__footer {
    display: flex;
    padding-top: 20px;
  }

  &__driver-phone {
    flex: 1 0;
    color: red;
    font-size: 32px;
  }

  &__vehicle-number {
    flex: 0 0 auto;
    padding-right: 20px;
    display: flex;
    align-items: flex-end;
    font-size: 26px;
  }

  &__cancel {
    position: absolute;
    top: 18%;
    left: 10%;
    z-index: 100;
    width: 600px;
    font-size: 180px;
    text-align: center;
    font-weight: normal;
    color: red;
    text-shadow: rgb(204, 204, 204) 10px 10px 20px;
  }
}
</style>
