import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const propsHttp = { home: '/api', url: 'fileName' };
  const headers = { Authorization: 'Bearer ' + user.token };
  const action = '/api/common/upload';
  const accept = 'image';
  const fileSize = 10000;
  const tip = '只能上传jpg/png，最多50张，且每张不超过10M';
  const limit = 50;
  const multiple = true;

  const column = [
    {
      label: '创建日期',
      prop: 'createTime',
      width: 180,
      align: 'center',
      display: false
    },
    {
      label: '图片',
      prop: 'picture',
      align: 'center',
      span: 24,
      rules: [{ required: true, message: '请输入' }],
      type: 'upload',
      fileSize,
      tip,
      limit,
      multiple,
      propsHttp,
      headers,
      action,
      accept
    }
  ];

  return { column };
};
