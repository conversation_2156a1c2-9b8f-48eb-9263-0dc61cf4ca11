<template>
  <div v-loading="loading" class="preview-page">
    <div class="preview-page__header">
      <el-space class="preview-page__mode">
        <span>排版</span>
        <el-radio-group size="large" v-model="mode">
          <el-radio-button value="3,4" label="3,4" />
          <el-radio-button value="3,3" label="3,3" />
          <el-radio-button value="3,2" label="3,2" />
        </el-radio-group>
      </el-space>
      <el-button class="preview-page__print" type="primary" @click="printing">
        打印
      </el-button>
    </div>
    <div class="preview-page__body">
      <el-image
        class="preview-page__picture"
        v-for="item in pictures"
        :src="item"
        :style="pictureStyle"
        fit="fill"
        alt="ticket"
      />
    </div>
    <el-backtop :bottom="100" />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useDetails } from '@/hooks';

const route = useRoute();

// 要查询的id
const { id } = route.query;

const { loading, data, load } = useDetails({
  id,
  url: 'youyang/ticket'
});

const pictures = computed(() => {
  return data.value?.picture?.split(',').map(item => {
    return `/api${item}`;
  });
});

const mode = ref('3,3');

const pageWidth = 210;
const pageHight = 297;

const size = computed(() => {
  return mode.value.split(',');
});

const pageCount = computed(() => {
  const [width, height] = size.value;
  return width * height;
});

const pictureStyle = computed(() => {
  const [width, height] = size.value;
  const actualWidth = pageWidth / width - 2;
  const actualHeight = pageHight / height - 2;
  return {
    width: actualWidth + 'mm',
    height: actualHeight + 'mm'
  };
});

function pageBreak(index) {
  const yes = pageCount.value == index + 1;
  if (yes) {
    return 'page-break-after: always;';
  }
}

function printing() {
  print();
}

load();
</script>

<style lang="scss">
@page {
  margin: 0;
  size: A4;
}
</style>

<style lang="scss" scoped>
.preview-page {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__header {
    display: flex;
    margin: 40px;

    @media print {
      display: none;
    }
  }

  &__mode {
    display: flex;
    align-items: center;
    margin-right: 20px;
    font-size: 20px;
  }

  &__print {
    width: 100px;
    height: 40px;
    font-size: 20px;
  }

  &__body {
    display: flex;
    gap: 2mm;
    flex-wrap: wrap;
    width: 210mm;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
