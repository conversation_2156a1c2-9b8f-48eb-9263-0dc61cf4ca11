<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #audit="{ row }">
      <dict-tag :value="row.audit" :options="yy_audit" />
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';
import { useDict } from '@/utils/dict';

defineOptions({ name: 'VehicleType' });

const { yy_audit } = useDict('yy_audit');

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/vehicleType',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 640,
    dialogDrag: true,
    labelWidth: 120,
    beforeAdd: setParams,
    beforeEdit: setParams
  });

// 数组参数逗号分隔
function setParams(params) {
  const { allowIds } = params;
  if (Array.isArray(allowIds)) {
    return { ...params, allowIds: allowIds.join(',') };
  } else {
    return params;
  }
}
</script>

<style lang="scss" scoped></style>
