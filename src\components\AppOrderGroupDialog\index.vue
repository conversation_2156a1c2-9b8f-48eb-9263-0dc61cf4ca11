<template>
  <el-dialog
    title="修改分组"
    :width="460"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
  >
    <avue-form
      v-model="form"
      :option="option"
      @submit="submit"
      @error="error"
    />
  </el-dialog>
</template>

<script setup>
defineOptions({ name: 'AppOrderGroupDialog', inheritAttrs: false });

const props = defineProps({
  option: Object
});

const emit = defineEmits(['update:form', 'submit', 'error']);

const form = defineModel('form');

// 仅转发事件
function submit(form, done) {
  emit('submit', form, done);
}

// 仅转发事件
function error(error) {
  emit('error', error);
}
</script>

<style lang="scss" scoped></style>
