import { reactive } from 'vue';

export default () => {
  const result = reactive({
    // 是否显示对话框
    visible: false,
    // 图标类型
    icon: '',
    // 标题
    title: '',
    // 子标题
    subTitle: '',
    // 消息
    messages: []
  });

  // 打开结果
  function resulting(
    data = {
      icon: 'success',
      title: '标题',
      subTitle: '子标题',
      messages: []
    }
  ) {
    const { icon, title, subTitle, messages } = data;
    result.icon = icon;
    result.title = title;
    result.subTitle = subTitle;
    result.messages = messages;
    result.visible = true;
  }

  // 关闭结果
  function resulted() {
    result.messages = [];
    result.visible = false;
  }

  return { result, resulting, resulted };
};
