<template>
  <div class="app-container order-upload">
    <app-result-dialog
      v-model="result.visible"
      v-bind="result"
      @close="resulted"
    />
    <div class="order-upload__item order-upload__item--ctrip">
      <p>
        <el-link
          type="primary"
          @click="templateDownload('订单导入模板_携程.xls')"
        >
          下载模板：携程
        </el-link>
      </p>
      <el-upload
        v-loading="ctrip.uploader.loading"
        action="/api/youyang/order/ctrip"
        :headers="headers"
        :show-file-list="false"
        accept="xlsx,xls"
        drag
        :on-change="ctrip.change"
        :on-progress="ctrip.progress"
        :on-success="ctrip.success"
        :on-error="ctrip.error"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到这里<em>或者单击此处选择文件</em>上传
        </div>
      </el-upload>
      <div class="order-upload__matching"></div>
    </div>
    <div class="order-upload__item">
      <p>
        <el-link
          type="primary"
          @click="templateDownload('订单导入模板_飞猪.xls')"
        >
          下载模板：飞猪
        </el-link>
      </p>
      <el-upload
        v-loading="fliggy.uploader.loading"
        action="/api/youyang/order/fliggy"
        :headers="headers"
        :show-file-list="false"
        accept="xlsx,xls"
        drag
        :on-change="fliggy.change"
        :on-progress="fliggy.progress"
        :on-success="fliggy.success"
        :on-error="fliggy.error"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到这里<em>或者单击此处选择文件</em>上传
        </div>
      </el-upload>
    </div>
  </div>
</template>

<script setup>
import useUserStore from '@/store/modules/user';
import { useResult, useUpload } from '@/hooks';
import { download } from '@/utils/request';

defineOptions({ name: 'OrderUpload' });

// 用户状态
const user = useUserStore();

// 结果展示对话框
const { result, resulting, resulted } = useResult();

const headers = { Authorization: 'Bearer ' + user.token };

const ctrip = useUpload({
  afterSuccess(response) {
    const { msg, code, data } = response;
    resulting({
      icon: 'success',
      title: '成功',
      subTitle: `${code}，${msg}`,
      messages: data
    });
  }
});

const fliggy = useUpload();

function templateDownload(name) {
  download(
    '/common/download/resource',
    { resource: `/profile/${name}` },
    name,
    { method: 'get' }
  );
}
</script>

<style lang="scss" scoped>
.order-upload {
  display: flex;
  gap: 20;

  &__item {
    flex: 1 0;
    padding: 20px;
    background-color: var(--el-color-white);

    &--ctrip {
      margin-right: 20px;
    }
  }

  &__matching {
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
}
</style>
