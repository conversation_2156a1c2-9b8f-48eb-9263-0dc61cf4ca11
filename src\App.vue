<template>
  <el-config-provider :locale="elementPlusLocale">
    <router-view />
  </el-config-provider>
</template>

<script setup>
import { onMounted } from 'vue';
import useLangStore from '@/store/modules/lang';
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import { storeToRefs } from 'pinia';

const langStore = useLangStore();

const settingStroe = useSettingsStore();

const { theme } = storeToRefs(settingStroe);

const { elementPlusLocale } = storeToRefs(langStore);

const { toggle } = langStore;

const lang = localStorage.getItem('lang') || 'zhCN';

// 切换语言
toggle(lang);

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(theme.value);
  });
});
</script>

<style lang="scss"></style>
