export default () => {
  const column = [
    {
      label: '名称',
      prop: 'name',
      headerAlign: 'center',
      minWidth: 160,
      search: true,
      searchSpan: 4,
      span: 24,
      cell: true,
      placeholder: '请输入'
    },
    {
      label: '经济5座',
      prop: 'eco5',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '经济7座',
      prop: 'eco7',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '舒适5v',
      prop: 'cosy5',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '舒适7座',
      prop: 'cosy7',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: 'H1',
      prop: 'h1',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '商务9座',
      prop: 'biz9',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: 'BenE300',
      prop: 'bene300',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: 'alphard',
      prop: 'alphard',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '10座中巴',
      prop: 'bus10',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '12座中巴',
      prop: 'bus12',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: '14座中巴',
      prop: 'bus14',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    },
    {
      label: 'Spiner 12座',
      prop: 'spiner12',
      align: 'center',
      width: 100,
      cell: true,
      type: 'number',
      controls: true,
      placeholder: '请输入',
      formatter
    }
  ];

  function formatter(row, value) {
    if (!value) {
      return '';
    } else {
      return `฿${value}`;
    }
  }

  return { column };
};
