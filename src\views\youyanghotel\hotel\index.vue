<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #body>
      <el-dialog
        v-model="visible"
        title="房型信息"
        :width="1200"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        draggable
      >
        <app-room-index :room="room" editable :other="{ height: '460px' }" />
      </el-dialog>
    </template>
    <template #menu-before="{ row }">
      <el-button text type="primary" icon="printer" @click="roomType(row)">
        房型信息
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import AppRoomIndex from '../room/index.vue';

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    name: '',
    url: '/youyanghotel/hotel',
    column: [
      {
        label: '酒店名称',
        prop: 'hname',
        align: 'center',
        width: 300,
        display: true,
        search: true,
        searchSpan: 4,
        rules: [{ required: true, message: '必填的' }]
      },
      {
        label: '电话',
        prop: 'phone',
        align: 'center',
        width: 260,
        search: true,
        searchSpan: 4,
        rules: [{ required: true, message: '必填的' }]
      },
      {
        label: '地址',
        prop: 'address',
        headerAlign: 'center',
        display: true,
        search: true,
        searchSpan: 4
      }
    ],
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    labelWidth: 120,
    menuWidth: 260
  });

// 是否显示管理对话框
const visible = ref(false);

// 管理的司机
const room = ref();

function roomType(row) {
  room.value = row;
  visible.value = true;
}
</script>

<style lang="scss" scoped></style>
