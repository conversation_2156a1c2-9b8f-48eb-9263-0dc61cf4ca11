<template>
  <el-dialog
    title="审核"
    :width="560"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
  >
    <app-order-form
      v-model="form"
      :option="option"
      @submit="submit"
      @error="error"
    />
  </el-dialog>
</template>

<script setup>
defineOptions({ name: 'AppVehicleAuditDialog', inheritAttrs: false });

const emit = defineEmits(['update:form', 'submit', 'error']);

const props = defineProps({option: Object });

const form = defineModel('form');

// 仅转发事件
function submit(form, done) {
  emit('submit', form, done);
}

// 仅转发事件
function error(error) {
  emit('error', error);
}
</script>

<style lang="scss" scoped></style>
