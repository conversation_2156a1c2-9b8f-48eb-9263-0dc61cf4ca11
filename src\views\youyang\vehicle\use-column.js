import { useDict } from '@/utils/dict';
import useUserStore from '@/store/modules/user';

export default options => {
  const { editable } = options;

  const { yy_brand, yy_color, yy_audit_status } = useDict(
    'yy_brand',
    'yy_color',
    'yy_audit_status'
  );

  // 用户状态
  const user = useUserStore();

  const search = !editable;

  const propsHttp = { home: '/api', url: 'fileName' };
  const listType = 'picture-img';
  const headers = { Authorization: 'Bearer ' + user.token };
  const action = '/api/common/upload';
  const accept = 'img';

  const column = [
    {
      label: '司机车队',
      prop: 'driverGroupId',
      align: 'center',
      width: 160,
      hide: editable,
      search,
      searchSpan: 4,
      type: 'select',
      display: false,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/vehicleGroup/all',
      props: { res: 'data', label: 'name', value: 'id' }
    },
    {
      label: '司机电话',
      prop: 'driverPhone',
      align: 'center',
      width: 160,
      hide: editable,
      search,
      searchSpan: 4,
      display: false
    },
    {
      label: '车型',
      prop: 'typeId',
      align: 'center',
      minWidth: 160,
      search,
      searchSpan: 4,
      type: 'select',
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/vehicleType/all',
      props: { res: 'data', label: 'name', value: 'id' },
      rules: [{ required: true, message: '请输入' }]
    },
    {
      label: '品牌',
      prop: 'brand',
      align: 'center',
      width: 160,
      type: 'select',
      dicData: yy_brand,
      rules: [{ required: true, message: '请输入' }]
    },
    {
      label: '颜色',
      prop: 'color',
      align: 'center',
      width: 160,
      type: 'select',
      dicData: yy_color,
      rules: [{ required: true, message: '请输入' }]
    },
    {
      label: '车牌',
      prop: 'number',
      align: 'center',
      minWidth: 160,
      search,
      searchSpan: 4,
      rules: [{ required: true, message: '请输入' }]
    },
    {
      label: '驾驶证照片',
      prop: 'licencePicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '保险证照片',
      prop: 'insurancePicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '人车合照',
      prop: 'peoplePicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '车前排照片',
      prop: 'frontPicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '车身照片',
      prop: 'bodyPicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '车后排照片',
      prop: 'backPicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '车后备箱照片',
      prop: 'trunkPicture',
      hide: true,
      span: 6,
      type: 'upload',
      propsHttp,
      listType,
      headers,
      action,
      accept
    },
    {
      label: '审核状态',
      search: true,
      prop: 'auditStatus',
      width: 140,
      align: 'center',
      search,
      searchSpan: 4,
      type: 'select',
      dicData: yy_audit_status,
      display: false
    },
    {
      label: '审核人',
      prop: 'auditBy',
      align: 'center',
      width: 160,
      display: false
    },
    {
      label: '审核时间',
      prop: 'auditTime',
      align: 'center',
      width: 160,
      hide: true,
      display: false
    },
    {
      label: '审核意见',
      prop: 'auditComment',
      headerAlign: 'center',
      width: 240,
      hide: true,
      display: false
    }
  ];

  return { column };
};
