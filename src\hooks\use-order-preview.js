import { reactive } from 'vue';

export default () => {
  const previewer = reactive({
    visible: false,
    // 选中的订单
    orders: [],
    // 是否中文
    chinese: false,
    // 排序方式
    sort: undefined
  });

  // 打开预览对话框
  function previewing(selected, chinese, sort) {
    previewer.orders = selected;
    previewer.chinese = chinese;
    previewer.sort = sort;
    previewer.visible = true;
  }

  // 新窗口预览
  function previewed(selected, chinese, sort) {
    previewer.orders = selected;
    previewer.chinese = chinese;
    previewer.sort = sort;
    const ids = selected.map(item => item.id).join(',');
    const isChinese = chinese ? '&chinese' : '';
    open(
      `/order/preview?&ids=${ids}${isChinese}&sort=${sort}`,
      '_black'
    );
  }

  return {
    previewer,
    previewing,
    previewed
  };
};
