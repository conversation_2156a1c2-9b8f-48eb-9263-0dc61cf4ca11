<template>
  <div class="price-calendar-container">
    <!-- 头部控制区 -->
    <div class="calendar-header">
      <div class="hotel-selector">
        <el-select v-model="selectedHotel" placeholder="选择酒店" style="width: 200px">
          <el-option
            v-for="hotel in hotels"
            :key="hotel.id"
            :label="hotel.name"
            :value="hotel.id"
          />
        </el-select>
      </div>

      <div class="header-controls">
        <el-button @click="previousWeek" icon="ArrowLeft" circle />
        <span class="date-range">{{ dateRangeText }}</span>
        <el-button @click="nextWeek" icon="ArrowRight" circle />
      </div>
    </div>

    <!-- 日历表格 -->
    <div class="calendar-table">
      <!-- 表头 -->
      <div class="table-header">
        <div class="room-type-header">房型房间</div>
        <div class="date-header">
          <div 
            v-for="(date, index) in dateColumns" 
            :key="index"
            class="date-column"
            :class="{ 'weekend': isWeekend(date.date) }"
          >
            <div class="weekday">{{ date.weekday }}</div>
            <div class="date">{{ date.display }}</div>
          </div>
        </div>
      </div>

      <!-- 房型数据行 -->
      <div class="table-body">
        <div 
          v-for="roomType in roomTypes" 
          :key="roomType.id"
          class="room-type-section"
        >
          <!-- 房型标题行 -->
          <div class="room-type-title-row">
            <div class="room-type-name">{{ roomType.name }}</div>
            <div class="room-type-dates">
              <div 
                v-for="(date, index) in dateColumns" 
                :key="index"
                class="date-cell empty-cell"
              ></div>
            </div>
          </div>

          <!-- 房间行 -->
          <div 
            v-for="room in roomType.rooms" 
            :key="room.id"
            class="room-row"
          >
            <div class="room-info">
              <div class="room-name">{{ room.name }}</div>
              <div class="room-details">
                <div class="detail-item">房价</div>
                <div class="detail-item">房量</div>
              </div>
            </div>
            <div class="room-dates">
              <div 
                v-for="(date, index) in dateColumns" 
                :key="index"
                class="date-cell"
                :class="{ 'weekend': isWeekend(date.date) }"
              >
                <div class="price-amount-cell" @click="editPriceAmount(room.id, date.date)">
                  <div class="price">{{ getRoomPrice(room.id, date.date) }}</div>
                  <div class="amount">{{ getRoomAmount(room.id, date.date) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 说明信息 -->
    <div class="calendar-footer">
      <div class="legend">
        <span class="legend-item">
          <span class="legend-color price-color"></span>
          房价
        </span>
        <span class="legend-item">
          <span class="legend-color amount-color"></span>
          房量
        </span>
        <span class="legend-item">
          <span class="legend-color weekend-color"></span>
          周末
        </span>
      </div>
      <div class="tips">
        点击价格房量单元格可进行编辑
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

// 当前显示的起始日期
const currentStartDate = ref(dayjs())

// 酒店数据
const hotels = ref([
  { id: 1, name: '豪华酒店' },
  { id: 2, name: '商务酒店' },
  { id: 3, name: '度假酒店' }
])

// 选中的酒店
const selectedHotel = ref(1)

// 生成日期列
const dateColumns = computed(() => {
  const columns = []
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  for (let i = 0; i < 14; i++) {
    const date = currentStartDate.value.add(i, 'day')
    columns.push({
      date: date.format('YYYY-MM-DD'),
      weekday: weekdays[date.day()],
      display: date.format('MM-DD'),
      dayOfWeek: date.day()
    })
  }
  return columns
})

// 日期范围文本
const dateRangeText = computed(() => {
  const start = currentStartDate.value.format('MM-DD')
  const end = currentStartDate.value.add(13, 'day').format('MM-DD')
  return `${start} - ${end}`
})

// 判断是否为周末
const isWeekend = (date) => {
  const day = dayjs(date).day()
  return day === 0 || day === 6
}

// 上一周
const previousWeek = () => {
  currentStartDate.value = currentStartDate.value.subtract(7, 'day')
}

// 下一周
const nextWeek = () => {
  currentStartDate.value = currentStartDate.value.add(7, 'day')
}

// 静态数据 - 房型和房间
const roomTypes = ref([
  {
    id: 1,
    name: '豪华型Deluxe Room',
    rooms: [
      {
        id: 101,
        name: '豪华-不可退'
      },
      {
        id: 102,
        name: '双床-不可退'
      }
    ]
  },
  {
    id: 2,
    name: '豪华型白金房Deluxe Balcony Room',
    rooms: [
      {
        id: 201,
        name: '内宾'
      }
    ]
  },
  {
    id: 3,
    name: '豪华型房间Grand Deluxe Room',
    rooms: [
      {
        id: 301,
        name: '豪华型房间Grand Deluxe Room'
      }
    ]
  },
  {
    id: 4,
    name: '豪华型双床房Grand Deluxe twin Room',
    rooms: [
      {
        id: 401,
        name: '豪华型双床房Grand Deluxe twin Room'
      }
    ]
  },
  {
    id: 5,
    name: '豪华型大床房Grand Suite',
    rooms: [
      {
        id: 501,
        name: '豪华型大床房Grand Suite'
      }
    ]
  }
])

// 静态价格数据 - 模拟不同日期的价格和房量
const priceData = ref({
  '101': { price: '1,000.00', amount: 5 },
  '102': { price: '1,200.00', amount: 3 },
  '201': { price: '1,500.00', amount: 2 },
  '301': { price: '1,800.00', amount: 4 },
  '401': { price: '1,600.00', amount: 6 },
  '501': { price: '2,000.00', amount: 1 }
})

// 生成随机价格和房量数据
const generateRandomPriceData = (roomId, date) => {
  const basePrice = priceData.value[roomId]?.price || '1,000.00'
  const baseAmount = priceData.value[roomId]?.amount || 5

  // 根据日期和房间ID生成一些变化
  const dateNum = parseInt(date.split('-')[2])
  const roomNum = parseInt(roomId)

  // 周末价格稍高
  const isWeekendDay = isWeekend(date)
  const priceMultiplier = isWeekendDay ? 1.2 : 1.0

  const price = Math.round(parseFloat(basePrice.replace(',', '')) * priceMultiplier * (0.8 + (dateNum % 5) * 0.1))
  const amount = Math.max(1, baseAmount + (dateNum % 3) - 1)

  return {
    price: price.toLocaleString() + '.00',
    amount: amount
  }
}

// 获取房间价格
const getRoomPrice = (roomId, date) => {
  const data = generateRandomPriceData(roomId, date)
  return data.price
}

// 获取房间数量
const getRoomAmount = (roomId, date) => {
  const data = generateRandomPriceData(roomId, date)
  return data.amount
}

// 编辑价格房量
const editPriceAmount = (roomId, date) => {
  console.log(`编辑房间 ${roomId} 在 ${date} 的价格和房量`)
  // 这里可以添加编辑对话框或其他交互逻辑
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.price-calendar-container {
  padding: 20px;
  background: #f5f5f5;
}

.calendar-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .hotel-selector {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .header-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .date-range {
      font-size: 16px;
      font-weight: bold;
      min-width: 120px;
      text-align: center;
    }
  }
}

.calendar-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;

  .room-type-header {
    width: 200px;
    padding: 15px;
    font-weight: bold;
    border-right: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e9ecef;
  }

  .date-header {
    flex: 1;
    display: flex;

    .date-column {
      flex: 1;
      padding: 8px 5px;
      text-align: center;
      border-right: 1px solid #e9ecef;
      min-height: 50px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &.weekend {
        background: #fff3cd;
        color: #856404;

        .weekday {
          color: #d63384;
        }

        .date {
          color: #d63384;
        }
      }

      .weekday {
        font-size: 11px;
        color: #666;
        margin-bottom: 2px;
      }

      .date {
        font-size: 13px;
        font-weight: bold;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.table-body {
  .room-type-section {
    border-bottom: 1px solid #e9ecef;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .room-type-title-row {
    display: flex;
    background: #f1f3f4;
    
    .room-type-name {
      width: 200px;
      padding: 12px 15px;
      font-weight: bold;
      border-right: 1px solid #e9ecef;
      background: #e9ecef;
    }
    
    .room-type-dates {
      flex: 1;
      display: flex;
      
      .date-cell {
        flex: 1;
        border-right: 1px solid #e9ecef;
        
        &:last-child {
          border-right: none;
        }
      }
    }
  }
  
  .room-row {
    display: flex;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .room-info {
      width: 200px;
      border-right: 1px solid #e9ecef;

      .room-name {
        padding: 8px 15px;
        font-weight: 500;
        border-bottom: 1px solid #f0f0f0;
        font-size: 13px;
      }

      .room-details {
        display: flex;

        .detail-item {
          flex: 1;
          padding: 6px 15px;
          font-size: 11px;
          color: #666;
          text-align: center;
          border-right: 1px solid #f0f0f0;
          background: #f8f9fa;

          &:last-child {
            border-right: none;
          }
        }
      }
    }

    .room-dates {
      flex: 1;
      display: flex;

      .date-cell {
        flex: 1;
        border-right: 1px solid #e9ecef;

        &.weekend {
          background: #fffbf0;
        }

        .price-amount-cell {
          display: flex;
          flex-direction: column;
          height: 60px;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background-color: #e3f2fd;
          }

          .price, .amount {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            border-bottom: 1px solid #f0f0f0;
          }

          .price {
            font-weight: bold;
            color: #e74c3c;
          }

          .amount {
            color: #27ae60;
            border-bottom: none;
          }
        }

        &:last-child {
          border-right: none;
        }
      }
    }
  }
}

.empty-cell {
  height: 40px;
}

.calendar-footer {
  margin-top: 20px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .legend {
    display: flex;
    gap: 20px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 12px;
      color: #666;

      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;

        &.price-color {
          background: #e74c3c;
        }

        &.amount-color {
          background: #27ae60;
        }

        &.weekend-color {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
        }
      }
    }
  }

  .tips {
    font-size: 12px;
    color: #999;
  }
}
</style>
