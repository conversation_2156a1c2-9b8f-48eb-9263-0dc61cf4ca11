<template>
  <div class="app-order-detail">
    <!-- 第1列 -->
    <div class="app-order-detail__column">
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">制单人：</span>
        <span>{{ customerBy }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">制单日期：</span>
        <span>{{ customerTime }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">订单：</span>
        <span>{{ sale }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">订单类型：</span>
        <dict-tag
          :value="type"
          :options="yy_order_type"
          style="display: inline"
        />
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">路线：</span>
        <span>{{ routeName }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">订单来源：</span>
        <dict-tag
          :value="source"
          :options="yy_order_source"
          style="display: inline"
        />
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">渠道来源：</span>
        <dict-tag
          :value="orderChannel"
          :options="yy_order_channel"
          style="display: inline"
        />
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">增值服务：</span>
        <span></span>
      </div>
      <div class="app-order-detail__cell">
        <span
          v-hasPermi="['youyang:order:price']"
          class="app-order-detail__label"
        >
          订单金额：
        </span>
        <span v-hasPermi="['youyang:order:price']">{{ price }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span
          v-hasPermi="['youyang:order:price']"
          class="app-order-detail__label"
        >
          利润：
        </span>
        <span v-hasPermi="['youyang:order:price']"></span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">客服备注：</span>
        <span class="app-order-detail__remark"></span>
      </div>
    </div>
    <!-- 第2列 -->
    <div class="app-order-detail__column">
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">归属人：</span>
        <span>{{ ownerBy }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">归属时间：</span>
        <span>{{ ownerTime }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">乘客姓名：</span>
        <span>{{ passengerName }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">乘客拼音：</span>
        <span>{{ passengerPinyin }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">成人人数：</span>
        <span>{{ number }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">儿童人数：</span>
        <span>{{ childrenNumber }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">行李数量：</span>
        <span>{{ baggageNumber }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">订单车型：</span>
        <span>{{ orderVehicleTypeName }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">乘客电话：</span>
        <span>{{ passengerPhone }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">乘客微信：</span>
        <span>{{ passengerWechat }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">乘客备注：</span>
        <span>{{ passengerRemark }}</span>
      </div>
    </div>
    <!-- 第3列 -->
    <div class="app-order-detail__column">
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">完成人：</span>
        <span>{{ completeBy }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">完成时间：</span>
        <span>{{ completeTime }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">抵达航班：</span>
        <span>{{ flight }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">接车时间：</span>
        <span>{{ vehicleTime }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">抵达航班：</span>
        <span>{{ flight }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">地点名称：</span>
        <span class="app-order-detail__overflow">
          <el-popover
            :content="hotelStartName"
            placement="top-start"
            :width="300"
          >
            <template #reference>
              {{ hotelStartName }}
            </template>
          </el-popover>
        </span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">起点地址：</span>
        <span class="app-order-detail__overflow">
          <el-popover
            :content="hotelStartAddress"
            placement="top-start"
            :width="300"
          >
            <template #reference>
              {{ hotelStartAddress }}
            </template>
          </el-popover>
        </span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">起点坐标：</span>
        <span>{{ hotelStartLonLat }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">终点名称：</span>
        <span class="app-order-detail__overflow">
          <el-popover
            :content="hotelEndName"
            placement="top-start"
            :width="300"
          >
            <template #reference>
              {{ hotelEndName }}
            </template>
          </el-popover>
        </span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">终点地址：</span>
        <span class="app-order-detail__overflow">
          <el-popover
            :content="hotelEndAddress"
            placement="top-start"
            :width="300"
          >
            <template #reference>
              {{ hotelEndAddress }}
            </template>
          </el-popover>
        </span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">终点坐标：</span>
        <span>{{ hotelEndLanLot }}</span>
      </div>
    </div>
    <!-- 第4列 -->
    <div class="app-order-detail__column">
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">接单信息：</span>
        <span></span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">指派人：</span>
        <span>{{ assignBy }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">指派时间：</span>
        <span>{{ assignTime }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">司机车队：</span>
        <span>{{ vehicleGroupName }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">司机电话：</span>
        <span>{{ driverPhone }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">车辆车牌：</span>
        <span>{{ vehicleNumber }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span class="app-order-detail__label">车辆车型：</span>
        <span>{{ vehicleTypeName }}</span>
      </div>
      <div class="app-order-detail__cell">
        <span
          v-hasPermi="['youyang:order:price']"
          class="app-order-detail__label"
        >
          扣款原因：
        </span>
        <span v-hasPermi="['youyang:order:price']"></span>
      </div>
      <div class="app-order-detail__cell">
        <span
          v-hasPermi="['youyang:order:price']"
          class="app-order-detail__label"
        >
          扣款金额：
        </span>
        <span v-hasPermi="['youyang:order:price']"></span>
      </div>
      <div class="app-order-detail__cell">
        <span
          v-hasPermi="['youyang:order:price']"
          class="app-order-detail__label"
        >
          处理结果：
        </span>
        <span v-hasPermi="['youyang:order:price']"></span>
      </div>
      <div class="app-order-detail__cell">
        <span
          v-hasPermi="['youyang:order:price']"
          class="app-order-detail__label"
        >
          结算金额：
        </span>
        <span v-hasPermi="['youyang:order:price']">{{ fee }}</span>
      </div>
    </div>
    <div class="app-order-detail__logs"></div>
  </div>
</template>

<script setup>
import { useDict } from '@/utils/dict';

defineOptions({ name: 'AppOrderDetail', inheritAttrs: false });

defineProps({
  sale: String,
  price: [Number, String],
  fee: [Number, String],
  type: String,
  orderChannel: String,
  source: String,
  status: String,
  orderVehicleTypeName: String,
  passengerName: String,
  passengerPinyin: String,
  passengerPhone: String,
  passengerWechat: String,
  passengerRemark: String,
  number: Number,
  childrenNumber: Number,
  baggageNumber: Number,
  flight: String,
  routeName: String,
  hotelStartName: String,
  hotelStartAddress: String,
  hotelStartLonLat: String,
  hotelEndName: String,
  hotelEndAddress: String,
  hotelEndLanLot: String,
  customerBy: String,
  customerTime: String,
  ownerBy: String,
  ownerTime: String,
  completeBy: String,
  completeTime: String,
  assignBy: String,
  assignTime: String,
  driverName: String,
  driverPhone: String,
  vehicleTime: String,
  vehicleNumber: String,
  vehicleTypeName: String,
  vehicleGroupName: String,
  arrived: String
});

const { yy_order_type, yy_order_channel, yy_order_source } = useDict(
  'yy_order_type',
  'yy_order_channel',
  'yy_order_source'
);
</script>

<style lang="scss" scoped>
.app-order-detail {
  display: flex;
  flex-wrap: wrap;
  border: thin solid var(--el-border-color);
  border-right: none;
  color: var(--el-text-color-primary);
  font-size: var(--el-font-size-base);
  font-weight: bold;

  &__column {
    flex: 0 0 25%;
    display: flex;
    flex-direction: column;
  }

  &__logs {
    flex: 0 0 100%;
    min-height: 200px;
    border-right: thin solid var(--el-border-color);
  }

  &__cell {
    flex: 1 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 6px 12px;
    border-right: thin solid var(--el-border-color);
    border-bottom: thin solid var(--el-border-color);
  }

  &__label {
    flex: 0 0 auto;
    display: flex;
    justify-content: end;
    align-items: center;
    min-width: 80px;
    color: var(--el-text-color-secondary);
  }

  &__overflow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 220px;
  }
}
</style>
