<template>
  <el-dialog
    title="指派"
    :width="1200"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @open="open"
    @close="close"
  >
    <avue-crud
      v-model:search="search"
      v-model:page="page"
      :table-loading="loading"
      :data="data"
      :option="option"
      @size-change="load"
      @current-change="load"
      @search-change="load"
      @search-reset="load"
      @refresh-change="load"
    >
      <template #sex="{ row }">
        <dict-tag :value="row.sex" :options="yy_sex" />
      </template>
      <template #menu-before="{ row }">
        <el-button
          size="small"
          icon="pointer"
          type="primary"
          plain
          @click="select(row)"
        >
          指派
        </el-button>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<script setup>
import { toRefs } from 'vue';
import { useCrud } from '@/hooks';
import { useDict } from '@/utils/dict';
import useColumn from './use-column';

defineOptions({ name: 'AppOrderAssignDialog', inheritAttrs: false });

const emit = defineEmits(['select']);

const props = defineProps({ orders: Array });

const { yy_sex } = useDict('yy_sex');

const { column } = useColumn();

// 大表哥
const { search, page, loading, data, option, load } = useCrud({
  name: '司机',
  url: '/youyang/vehicle',
  defaultSearch: {
    // audit: 1, 还不能使用，要有审核实现和交互
    status: '1'
  },
  column,
  searchLabelWidth: 100,
  header: false,
  searchSpan: 6,
  columnBtn: false,
  gridBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  expand: false,
  selection: false,
  menuWidth: 140,
  menuAlign: 'center'
});

async function select(row) {
  emit('select', row);
}
function open() {
  load();
}

function close() {
  search.value = [];
  page.value.currentPage = 1;
  data.value = [];
}
</script>

<style lang="scss" scoped></style>
