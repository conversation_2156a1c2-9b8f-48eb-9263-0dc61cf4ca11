import { ref } from 'vue';
import { defineStore } from 'pinia';
import { useI18n } from 'vue-i18n';
import zhCN from 'element-plus/es/locale/lang/zh-cn';
import th from 'element-plus/es/locale/lang/th';

const useLangStore = defineStore('lang', () => {
  const { locale } = useI18n({ useScope: 'global' });

  const elementPlusLocale = ref();

  function toggle(lang) {
    switch (lang) {
      case 'th':
        localStorage.setItem('lang', 'th');
        elementPlusLocale.value = th;
        locale.value = 'th';
        break;
      default:
        localStorage.setItem('lang', 'zhCN');
        elementPlusLocale.value = zhCN;
        locale.value = 'zhCN';
        break;
    }
  }

  return { elementPlusLocale, toggle };
});

export default useLangStore;
