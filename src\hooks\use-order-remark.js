import { reactive } from 'vue';

export default () => {
  const remarker = reactive({
    // 是否显示对话框
    visible: false,
    // 选中订单
    orderId: undefined,
    // 销售订单号
    sael: undefined,
    // 传入对话框的模式
    mode: 'remark'
  });

  // 显示备注对话框
  function remarking(row) {
    remarker.orderId = row.id;
    remarker.sale = row.sale;
    remarker.visible = true;
  }

  return { remarker, remarking };
};
