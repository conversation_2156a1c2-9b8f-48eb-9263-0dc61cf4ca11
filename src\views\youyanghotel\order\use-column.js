import { useDict } from '@/utils/dict';

export default () => {
  const { yh_confirm_status, yh_status } = useDict(
    'yh_confirm_status',
    'yh_status'
  );

  const column = [
    {
      label: '导入时间',
      prop: 'createTime',
      align: 'center',
      width: 180,
      type: 'date',
      display: false,
      search: true,
      searchSpan: 4
    },
    {
      label: '订单号',
      prop: 'sale',
      align: 'center',
      width: 180,
      search: true,
      searchSpan: 4,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '城市',
      prop: 'city',
      align: 'center',
      width: 100
    },
    {
      label: '入住日期',
      prop: 'checkInRange',
      align: 'center',
      width: 200,
      formatter(row) {
        return `${row.checkInStartDate}至${row.checkInEndDate}`;
      },
      type: 'daterange',
      startPlaceholder: '入住日期',
      endPlaceholder: '退房日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '天数',
      prop: 'days',
      align: 'center',
      width: 60,
      display: false
    },
    {
      label: '入住规则',
      prop: 'cancelled',
      align: 'center',
      width: 100,
      hide: true,
      type: 'radio',
      value: '0',
      dicData: [
        { label: '不可取消', value: '0' },
        { label: '免费取消', value: '1' }
      ]
    },
    {
      label: '顾客姓名',
      prop: 'customerName',
      align: 'center',
      width: 180,
      hide: true,
      search: true,
      searchSpan: 4,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '成人',
      prop: 'number',
      align: 'center',
      width: 80,
      hide: true,
      span: 6,
      type: 'number',
      rules: [{ required: true, message: '必填的' }],
      precision: 0,
      min: 1,
      max: 20,
      controls: false
    },
    {
      label: '儿童',
      prop: 'childrenNumber',
      align: 'center',
      width: 80,
      hide: true,
      span: 6,
      type: 'number',
      precision: 0,
      min: 1,
      max: 10,
      controls: false
    },
    {
      label: '是否含餐',
      prop: 'breakfast',
      align: 'center',
      width: 100,
      span: 6,
      type: 'radio',
      value: '0',
      dicData: [
        { label: '否', value: '0' },
        { label: '是', value: '1' }
      ]
    },
    {
      label: '含餐数量',
      prop: 'breakfastNumber',
      align: 'center',
      width: 80,
      hide: true,
      span: 6,
      row: true,
      type: 'number',
      precision: 0,
      min: 1,
      max: 20,
      controls: false
    },

    {
      label: '酒店',
      prop: 'hotelName',
      align: 'center',
      minWidth: 180,
      search: true,
      searchSpan: 4,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '酒店电话',
      prop: 'hotelPhone',
      align: 'center',
      width: 100,
      hide: true
    },
    {
      label: '酒店地址',
      prop: 'hotelAddress',
      align: 'center',
      width: 180,
      hide: true,
      type: 'textarea'
    },
    {
      label: '备注',
      prop: 'remark',
      headerAlign: 'center',
      width: 240,
      hide: true,
      type: 'textarea'
    },
    {
      label: '客房类型',
      prop: 'productName',
      headerAlign: 'center',
      width: 180,
      hide: true
    },
    {
      label: '客房数量',
      prop: 'roomNumber',
      align: 'center',
      hide: true,
      width: 80,
      type: 'number',
      precision: 0,
      min: 1,
      max: 20,
      controls: false
    },
    {
      label: '价格(B)',
      prop: 'price',
      align: 'center',
      width: 120
    },
    {
      label: '底价(B)',
      prop: 'cost',
      align: 'center',
      width: 120
    },
    {
      label: '确认状态',
      prop: 'confirmStatus',
      align: 'center',
      width: 180,
      hide: true,
      search: true,
      searchSpan: 4,
      display: false,
      type: 'select',
      dicData: yh_confirm_status
    },
    {
      label: '状态',
      prop: 'status',
      align: 'center',
      width: 180,
      hide: true,
      search: true,
      searchSpan: 4,
      display: false,
      type: 'select',
      dicData: yh_status
    }
  ];

  return { column };
};
