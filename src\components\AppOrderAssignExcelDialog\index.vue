<template>
  <el-dialog
    title="导入指派"
    :width="800"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
  >
    <h3>模版填写指南</h3>
    <p>
      (1)若司机綁定车辆，【司机ID】或【司机电话】
      二选一填写，【订单号】、【指派/更改】为必填，其余项选填;
    </p>
    <p>
      (2)若司机未绑定车辆，【司机ID】或【司机电话】
      二选一填写，【车牌号】或【车辆编码】二选一填写，【订单号】、【车牌号】、【指派/更改】为必填，其余项选填
    </p>
    <p>
      <el-link
        type="primary"
        @click="templateDownload('订单批量指派模板_携程.xlsx')"
      >
        下载批量指派模板
      </el-link>
    </p>
    <el-upload
      v-loading="loading"
      action="/api/youyang/order/assignsExcel"
      :headers="headers"
      :show-file-list="false"
      accept="xlsx,xls"
      drag
      :on-change="change"
      :on-success="success"
      :on-error="error"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">
        将文件拖到这里<em>或者单击此处选择文件</em>上传
      </div>
    </el-upload>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import useUserStore from '@/store/modules/user';
import { download } from '@/utils/request';

defineOptions({ name: 'AppOrderAssignExcelDialog', inheritAttrs: 'false' });

const emit = defineEmits(['success']);

// 用户状态
const user = useUserStore();

const headers = { Authorization: 'Bearer ' + user.token };

const loading = ref(false);

function change(uploadFile) {
  if (uploadFile.status == 'ready') {
    loading.value = true;
  }
}

function success(response) {
  loading.value = false;
  if (response.code == 200) {
    ElMessage.success(response.msg);
    emit('success', response);
  } else {
    ElMessage.error(response.msg);
  }
}

function error(error) {
  loading.value = false;
  uploader.indeterminate = false;
  ElMessage.error(error.message);
}

function templateDownload(name) {
  download(
    '/common/download/resource',
    { resource: `/profile/${name}` },
    name,
    { method: 'get' }
  );
}
</script>

<style lang="scss" scoped></style>
