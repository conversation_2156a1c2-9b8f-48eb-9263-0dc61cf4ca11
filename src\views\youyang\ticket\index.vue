<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #menu-before="{ row }">
      <el-button text type="primary" icon="printer" @click="previewing(row)">
        打印
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';
import { useDict } from '@/utils/dict';

defineOptions({ name: 'Ticket' });

const { yy_ticket } = useDict('yy_ticket');

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/ticket',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 800,
    dialogDrag: true,
    labelWidth: 120,
    index: true,
    menu: true,
    addBtn: true,
    editBtn: true,
    delBtn: true,
    beforeAdd: setParams
  });

// 数组参数逗号分隔
function setParams(_form) {
  const { picture } = _form;
  if (Array.isArray(picture)) {
    _form.picture = picture.join(',');
  }
  return _form;
}

// 新窗口预览
function previewing(row) {
  open(`/order/note?&id=${row.id}`, '_black');
}
</script>

<style lang="scss" scoped>
.picture {
  display: flex;
  margin: 0 10px;
  gap: 10px;

  &__item {
    border: thin solid var(--el-border-color);
    width: 220px;
    height: 200px;
  }
}
</style>
