import useDictStore from '@/store/modules/dict';
import { getDicts } from '@/api/system/dict/data';
import { useI18n } from 'vue-i18n';

/**
 * 获取字典数据
 */
export function useDict(...args) {
  const { locale, t, te } = useI18n();

  const isThai = locale.value == 'th';

  const result = ref({});

  return (() => {
    args.forEach((dictType, index) => {
      result.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        result.value[dictType] = dicts;
      } else {
        getDicts(dictType).then(resp => {
          result.value[dictType] = resp.data.map(p => {
            // 国际化label
            let label = p.dictLabel;
            if (isThai && te(`dict.${dictType}.${p.dictValue}`)) {
              label = t(`dict.${dictType}.${p.dictValue}`);
            }
            return {
              label,
              value: p.dictValue,
              remark: p.remark,
              elTagType: p.listClass,
              elTagClass: p.cssClass
            };
          });
          useDictStore().setDict(dictType, result.value[dictType]);
        });
      }
    });
    return toRefs(result.value);
  })();
}
