<template>
  <el-dialog
    title="预览"
    :width="1200"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
  >
    <template v-for="item in orders">
      <div class="app-order-preview">
        <app-order-preview-chinese v-if="chinese" v-bind="item" />
        <app-order-preview v-else v-bind="item" />
      </div>
    </template>
    <template #footer>
      <el-button
        icon="printer"
        type="primary"
        @click="previewed(orders, chinese, sort)"
      >
        打印
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { useOrderPreview } from '@/hooks';

defineOptions({ name: 'AppOrderPreviewDialog', inheritAttrs: false });

const props = defineProps({
  orders: Array,
  chinese: Boolean,
  sort: String,
  direction: String
});

const { previewed } = useOrderPreview();
</script>

<style lang="scss" scoped>
.app-order-preview {
  display: flex;
  justify-content: center;
}
</style>
