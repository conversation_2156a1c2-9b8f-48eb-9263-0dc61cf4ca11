import { createApp } from 'vue'; // vue3
import { createI18n } from 'vue-i18n'; // 国际化
import Cookies from 'js-cookie'; // cookie
import ElementPlus from 'element-plus'; // element-plus 组件库
import 'element-plus/dist/index.css'; // element-plus 样式
import Avue from '@smallwei/avue'; // 大表哥
import '@smallwei/avue/lib/index.css';
import VueViewer from 'v-viewer'; // 图片预览组件
import 'viewerjs/dist/viewer.css';
import './assets/styles/index.scss'; // 全局样式
import messages from './locale'; // 翻译文件
import App from './App'; // 入口组件
import store from './store'; // 存储
import router from './router'; // 路由
import globalComponents from './components'; // 全局组件
import plugins from './plugins'; // 插件
import directive from './directive'; // 指令
import './permission'; // 权限控制

// 创建应用
const app = createApp(App);

// 注册存储
app.use(store);

// 注册路由
app.use(router);

// 国际化
const i18n = createI18n({
  legacy: false,
  // 在App.vue内切换语言
  fallbackLocale: 'zhCN',
  messages
});

// 注册国际化
app.use(i18n);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  // 在App.vue内切换语言
  // 支持 large、default、small
  size: Cookies.get('size') || 'default'
});

// 注册大表哥组件库，基于element-plus
app.use(Avue, Cookies.get('size'));

// 图片预览组件
app.use(VueViewer, {
  defaultOptions: {
    zIndex: 5000
  }
});

// 注册全局组件
app.use(globalComponents);

// 注册插件
app.use(plugins);

// 注册指令
directive(app);

// 挂载
app.mount('#app');
