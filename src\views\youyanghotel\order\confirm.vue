<template>
  <div class="confirm-page">
    <div class="confirm-page__body">
      <app-hotel-order-confirm v-loading="loading" v-bind="data" />
    </div>
    <div class="confirm-page__footer">
      <el-button class="confirm-page__print" type="primary" @click="printing">
        打印
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { useDetails } from '@/hooks';

const route = useRoute();

const id = route.query.id;

const { loading, data, load } = useDetails({
  id,
  url: 'youyanghotel/order'
});

function printing() {
  print();
}

load();
</script>

<style lang="scss" scoped>
@page {
  margin: 1cm;
  size: A4 portrait;
}

.confirm-page {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;

  &__header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px;
  }

  &__footer {
    display: flex;
    flex-direction: column;
    padding: 20px;

    @media print {
      display: none;
    }
  }

  &__print {
    width: 100px;
    height: 40px;
    font-size: 20px;
  }
}
</style>
