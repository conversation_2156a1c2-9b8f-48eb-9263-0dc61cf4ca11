<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  />
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';

defineOptions({ name: 'ConfGroup' });

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/orderGroup',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    labelWidth: 120,
    beforeAdd: setParams,
    beforeEdit: setParams
  });

// 数组参数逗号分隔
function setParams(_form) {
  const { wechatIds, userIds } = _form;
  if (Array.isArray(wechatIds)) {
    _form.wechatIds = wechatIds.join(',');
  }
  if (Array.isArray(userIds)) {
    _form.userIds = userIds.join(',');
  }
  return _form;
}
</script>

<style lang="scss" scoped></style>
