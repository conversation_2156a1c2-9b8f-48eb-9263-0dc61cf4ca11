<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #body>
      <el-dialog
        v-model="visible"
        title="车辆管理"
        :width="1600"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        draggable
      >
        <app-vehicle-page
          :driver="driver"
          editable
          :other="{ height: '460px' }"
        />
      </el-dialog>
    </template>
    <template #sex="{ row }">
      <dict-tag :value="row.sex" :options="yy_sex" />
    </template>
    <template #status="{ row }">
      <dict-tag :value="row.status" :options="yy_enable" />
    </template>
    <template #menu-before="{ row }">
      <el-button icon="van" type="primary" text @click="open(row)">
        车辆管理
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { ref } from 'vue';
import { useCrud } from '@/hooks';
import useColumn from './use-column';
import { useDict } from '@/utils/dict';
import AppVehiclePage from '../vehicle/index';

defineOptions({ name: 'Driver' });

const { yy_sex, yy_enable } = useDict('yy_sex', 'yy_enable');

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/driver',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 640,
    dialogDrag: true,
    labelWidth: 120,
    menuWidth: 260
  });

// 是否显示管理对话框
const visible = ref(false);

// 管理的司机
const driver = ref();

// 打开管理对话框
function open(row) {
  driver.value = row;
  visible.value = true;
}
</script>

<style lang="scss" scoped></style>
