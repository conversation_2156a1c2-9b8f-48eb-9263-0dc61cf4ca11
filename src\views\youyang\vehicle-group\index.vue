<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #audit="{ row }">
      <dict-tag :value="row.audit" :options="yy_audit" />
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import { useDict } from '@/utils/dict';

defineOptions({ name: 'VehicleGroup' });

const { yy_audit } = useDict('yy_audit');

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/vehicleGroup',
    column: [
      {
        label: '名称',
        prop: 'name',
        width: 320,
        align: 'center',
        span: 24,
        search: true,
        searchSpan: 4,
        rules: [{ required: true, message: '必填的' }]
      }
    ],
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    labelWidth: 120
  });
</script>

<style lang="scss" scoped></style>
