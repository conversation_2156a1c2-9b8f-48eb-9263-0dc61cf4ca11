<template>
  <el-dialog
    title="选择地址"
    :width="1200"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @open="open"
    @close="close"
  >
    <avue-crud
      v-model:search="search"
      v-model:page="page"
      :table-loading="loading"
      :data="data"
      :option="option"
      @search-change="load"
      @search-reset="load"
      @refresh-change="load"
    >
      <template #menu-before="{ row }">
        <el-button
          size="small"
          icon="pointer"
          type="primary"
          plain
          @click="$emit('select', row)"
        >
          选择
        </el-button>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';
import { toRefs } from 'vue';

defineOptions({ name: 'AppHotelSelectionDialog', inheritAttrs: 'false' });

defineEmits(['select']);

const props = defineProps({ fullname: String });

const { fullname } = toRefs(props);

const { column } = useColumn();

// 大表哥
const { search, page, loading, data, option, load } = useCrud({
  name: '地址选择',
  url: '/youyang/hotel',
  column,
  searchLabelWidth: 100,
  columnBtn: false,
  gridBtn: false,
  labelWidth: 120,
  header: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 160
});

function open() {
  search.value.fullname = fullname.value;
  load();
}

function close() {
  data.value = [];
}
</script>

<style lang="scss" scoped></style>
