<template>
  <avue-form ref="formRef" v-model="form" v-bind="$attrs">
    <template #sale="{ column }">
      <el-input v-model="form.sale" v-bind="column">
        <template #append>
          <el-button v-bind="column" @click="$emit('generate', form)">
            生成
          </el-button>
        </template>
      </el-input>
    </template>
    <template #passengerPhone="{ column }">
      <el-input
        v-model="form.passengerPhone"
        v-bind="column"
        placeholder="请输入 乘客电话"
        @blur="$emit('passenger-phone-blur', form.passengerPhone)"
      />
    </template>
    <template #match>
      <el-button
        :loading="loading"
        plain
        icon="location"
        type="primary"
        @click="$emit('match', form)"
      >
        匹配
      </el-button>
    </template>
  </avue-form>
</template>

<script setup>
import { ref } from 'vue';

defineOptions({ name: 'AppOrderForm', inheritAttrs: false });

defineProps({ loading: Boolean });

defineEmits(['generate', 'match', 'passenger-phone-blur']);

defineExpose({ resetForm });

const formRef = ref();

const form = defineModel();

function resetForm() {
  formRef.value.resetForm();
}
</script>

<style lang="scss" scoped>
.avue-form {
  margin: 0;
  padding: 20px;
  background-color: white;
}
</style>
