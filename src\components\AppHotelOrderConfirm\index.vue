<template>
  <div v-loading="loading" class="app-confirm">
    <div class="app-confirm__header">
      酒店预订
      <span class="app-confirm__title">确认单</span>
      <span class="app-confirm__subtitle">Booking Confirmation</span>
    </div>
    <div class="app-confirm__divide"></div>
    <div class="app-confirm__body">
      <div class="app-confirm__section">
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Booking ID：</div>
            <div>订单号：</div>
          </div>
          <div class="app-confirm__value">{{ sale }}</div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Client：</div>
            <div>顾客姓名：</div>
          </div>
          <div class="app-confirm__value">{{ customerName }}</div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>City：</div>
            <div>城市：</div>
          </div>
          <div class="app-confirm__value">{{ city }}</div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Property：</div>
            <div>酒店：</div>
          </div>
          <div class="app-confirm__value app-confirm__value--border">
            {{ hotelName }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Address：</div>
            <div>地址：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--border app-confirm__value--large"
          >
            {{ hotelAddress }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Contact Number：</div>
            <div>酒店电话号码：</div>
          </div>
          <div class="app-confirm__value app-confirm__value--border">
            {{ hotelPhone }}
          </div>
        </div>
      </div>
      <div class="app-confirm__section app-confirm__section--bg">
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Number of Rooms：</div>
            <div>客房数量</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ roomNumber }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Number of Adults：</div>
            <div>成人人数：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ number }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Number of Children：</div>
            <div>儿童人数：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ childrenNumber }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Room Type：</div>
            <div>房型：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ productName }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Breakfast：</div>
            <div>早餐：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ breakfastNumber }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Currency：</div>
            <div>币种：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ currency }}
          </div>
        </div>
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Price({{ currency }})：</div>
            <div>房价：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--lightborder app-confirm__value--center"
          >
            {{ price }}
          </div>
        </div>
      </div>
    </div>
    <div class="app-confirm__footer">
      <div class="app-confirm__section">
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Arrival：</div>
            <div>入住日期：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--bg app-confirm__value--newline"
          >
            <div>{{ checkInStartDateEn }}</div>
            <div>{{ checkInStartDate }}</div>
          </div>
        </div>
      </div>
      <div class="app-confirm__section">
        <div class="app-confirm__row">
          <div class="app-confirm__label">
            <div>Departure：</div>
            <div>退房日期：</div>
          </div>
          <div
            class="app-confirm__value app-confirm__value--bg app-confirm__value--newline"
          >
            <div>{{ checkInEndDateEn }}</div>
            <div>
              {{ checkInEndDate }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';

dayjs.extend(localizedFormat);

defineOptions({ name: 'AppHotelOrderConfirm', inheritAttrs: false });

const props = defineProps({
  sale: String,
  customerName: String,
  city: String,
  hotelName: String,
  hotelAddress: String,
  hotelPhone: String,
  roomNumber: String,
  number: String,
  childrenNumber: String,
  roomTypeName: String,
  productName: String,
  breakfastNumber: String,
  currency: String,
  price: Number,
  checkInStartDate: String,
  checkInEndDate: String
});

const checkInStartDateEn = computed(() => {
  return dayjs(props.checkInStartDate).format('LL');
});

const checkInEndDateEn = computed(() => {
  return dayjs(props.checkInEndDate).format('LL');
});
</script>

<style lang="scss" scoped>
.app-confirm {
  display: flex;
  flex-direction: column;
  border: thin solid black;
  padding: 12px;
  width: 700px;
  font-weight: bold;
  font-size: 14px;

  &__header {
    display: flex;
    justify-content: center;
    margin-bottom: 12px;
    font-size: 32px;
  }

  &__title {
    color: red;
  }

  &__subtitle {
    display: flex;
    align-items: flex-end;
    margin-left: 12px;
    color: red;
    font-size: 18px;
  }

  &__divide {
    margin-bottom: 12px;
    height: 20px;
    background: var(--el-border-color-dark);
  }

  &__body {
    display: flex;
  }

  &__section {
    flex: 1 0;
    display: flex;
    flex-direction: column;
    padding: 12px;

    &--bg {
      background-color: var(--el-border-color);
    }
  }

  &__row {
    flex: 1 0;
    display: flex;
    padding-bottom: 6px;

    &:last-child {
      padding-bottom: 0;
    }
  }

  &__label {
    flex: 1 0;
    display: flex;
    flex-direction: column;
  }

  &__value {
    flex: 2 0;
    display: flex;
    align-items: center;
    padding: 6px 12px;

    &--border {
      border: 2px solid var(--el-border-color-dark);
    }

    &--lightborder {
      border: 2px solid white;
    }

    &--large {
      align-items: flex-start;
      height: 120px;
    }

    &--center {
      justify-content: center;
    }

    &--bg {
      background-color: var(--el-border-color);
    }

    &--newline {
      flex-direction: column;
    }
  }

  &__center {
    display: flex;
    justify-content: center;
  }

  &__footer {
    display: flex;
    margin-top: 12px;
    border: 2px solid var(--el-border-color-dark);
  }
}
</style>
