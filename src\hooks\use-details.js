import { ref, unref } from 'vue';
import { ElMessage } from 'element-plus';
import request from '@/utils/request';

// 详情功能
export default options => {
  const {
    id, // 编号
    url, // 详情url
    before, // 请求之前回调
    after // 请求之后回调
  } = options;

  // 是否加载中
  const loading = ref(false);

  // 数据
  const data = ref({});

  // 加载
  async function load() {
    try {
      const _id = unref(id);
      if (!_id || id == '') {
        return;
      }
      loading.value = true;
      if (before && typeof before == 'function') {
        before(_id);
      }
      const detailUrl = `${url}/${_id}`;
      const result = await request.get(detailUrl);
      data.value = result.data;
      if (after && typeof after == 'function') {
        after(data.value);
      }
    } catch (error) {
      ElMessage.error(error.message);
    } finally {
      loading.value = false;
    }
  }

  return {
    loading,
    data,
    load
  };
};
