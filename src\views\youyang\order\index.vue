<template>
  <avue-crud
    ref="crudRef"
    class="app-container order"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    header-row-class-name="order__header"
    row-class-name="order__body"
    :cell-class-name="cellClassName"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
    @selection-change="select"
    @keydown="keydwon"
  >
    <template #search>
      <el-tabs v-model="active" @tab-change="tagChange">
        <el-tab-pane :label="t('order.tabs.unassgin')" name="1" />
        <el-tab-pane :label="t('order.tabs.assigned')" name="3" />
        <el-tab-pane :label="t('order.tabs.cancelled')" name="4" />
        <el-tab-pane :label="t('order.tabs.deleted')" name="5" />
        <el-tab-pane :label="t('order.tabs.completed')" name="60" />
        <el-tab-pane v-if="thaiFalse" :label="t('order.tabs.care')" name="70" />
        <el-tab-pane
          v-if="thaiFalse"
          :label="t('order.tabs.question')"
          name="100"
        />
        <el-tab-pane :label="t('order.tabs.all')" name="" />
      </el-tabs>
    </template>
    <template #menu-left>
      <app-order-toolbar
        :toggle-text="toggleText"
        :can-select="canSelect"
        @toggle-all="toggleSelection"
        @logiced="logiced(selected)"
        @preview-chinese="previewed(selected, false, search.sort)"
        @preview="previewed(selected, true, search.sort)"
        @assign="assigning(selected)"
        @group="groupOpen"
        @assign-excel="assigner.visibleExcel = true"
        @export-finance="exportFinance"
        @export-vehicle-sync="exportVehicleSync"
        @copy="copy(data)"
      />
    </template>
    <template #menu-right>
      <el-space class="order__stats">
        <el-text size="large">
          {{ t('order.stats.today') }}:{{ stats.total }}
        </el-text>
        <el-text size="large">
          {{ t('order.stats.completed') }}:{{ stats.completed }}
        </el-text>
        <el-text size="large">
          {{ t('order.stats.cancelled') }}:{{ stats.cancelled }}
        </el-text>
        <el-text size="large">
          {{ t('order.stats.remaining') }}:{{ stats.remaining }}
        </el-text>
      </el-space>
    </template>
    <template #body>
      <app-result-dialog
        v-model="result.visible"
        v-bind="result"
        @close="resulted"
      />
      <app-order-assign-dialog
        v-model="assigner.visible"
        v-bind="assigner"
        @select="assigned"
      />
      <app-order-assign-excel-dialog
        v-model="assigner.visibleExcel"
        @success="assignExcelSuccess"
      />
      <app-order-group-dialog
        v-model="groupVisible"
        v-model:form="groupForm"
        :option="groupOption"
        @submit="groupSubmit"
        @error="groupError"
      />
      <app-order-owner-dialog
        v-model="ownerVisible"
        v-model:form="ownerForm"
        :option="ownerOption"
        @submit="ownerSubmit"
        @error="ownerError"
      />
      <app-order-passenger-dialog
        v-model="passenger.visible"
        v-bind="passenger.order"
      />
      <app-order-vehicle-dialog v-model="vehicle.visible" v-bind="vehicle" />
      <app-order-question-dialog v-model="question.visible" v-bind="question" />
      <app-order-question-dialog
        v-model="remarker.visible"
        v-bind="remarker"
        @complete="remarked"
      />
      <app-order-hotel-form-dialog
        v-model="hotelVisible"
        v-model:form="hotelForm"
        :option="hotelOption"
        @submit="hotelSubmit"
        @error="hotelError"
      />
      <app-order-logging-dialog v-model="logger.visible" v-bind="logger" />
      <app-order-ticket-dialog v-model="ticketer.visible" v-bind="ticketer" />
      <app-order-preview-dialog
        v-model="previewer.visible"
        v-bind="previewer"
      />
      <app-order-detail-dialog
        v-model="detailer.visible"
        v-bind="detailer.order"
      />
      <app-order-form-dialog
        v-model="editVisible"
        v-model:form="editForm"
        :option="editOption"
        :loading="matcher.loading"
        @submit="editSubmit"
        @error="editError"
        @match="matching"
        @passenger-phone-blur="matchCustomerWechat"
      />
      <app-order-hotel-matcher-dialog
        v-model="matcher.visible"
        v-model:form="editForm"
        @match="matched"
      />
      <el-backtop :bottom="100" />
    </template>
    <template #expand="{ row }">
      <app-order
        v-if="row.id"
        ref="orderRef"
        :id="row.id"
        :row="row"
        :remarks="remarker.data"
        @change-phone-color="setPhoneIcon"
        @change-wechat-color="setWechatIcon"
        @change-vehicle-color="setVehicleIcon"
        @change-exit-port="setExitPort(row, $event)"
        @passenger="passengerDetail"
        @driver="vehicleDetail"
        @question="questioning"
        @assign="assigning([row])"
        @unassign="unassigned"
        @clone="clone"
        @hotel-start="hotelOpen"
        @hotel-end="hotelOpen"
        @log="logging"
        @preview="previewing([row])"
        @preview-chinese="previewing([row], true)"
        @own="ownerOpen"
        @edit="editiOpen"
        @detail="detailing"
        @cancel="canceled"
        @logic="logiced([row])"
        @complete="completed"
        @remark="remarking"
        @ticket="ticketing"
      />
    </template>
    <template v-if="thaiTrue" #source>--</template>
    <template #passengerName="{ row }">
      <el-image
        v-if="row.hotelStartLevel == 5 || row.hotelEndLevel == 5"
        class="order__icon"
        :src="loveRed"
        alt="love"
      />
      {{ row.passengerName }}
    </template>
    <template #customerWechat="{ row }">
      <el-space>
        <el-button text circle @click.stop="setWechatIcon(row)">
          <el-image
            class="order__icon"
            :src="getWechatIcon(row)"
            alt="wechat"
          />
        </el-button>
        <span>{{ row.customerWechat }}</span>
      </el-space>
    </template>
    <template #arrived="{ row }">
      <el-space>
        <el-button text circle @click.stop="setVehicleIcon(row)">
          <el-image
            class="order__icon"
            :src="getVehicleIcon(row)"
            alt="vehicle"
          />
        </el-button>
      </el-space>
    </template>
    <template #passengerPhoneColor="{ row }">
      <el-button text circle @click.stop="setPhoneIcon(row)">
        <el-image class="order__icon" :src="getPhoneIcon(row)" alt="phone" />
      </el-button>
    </template>
    <template #passengerImColor="{ row }">
      <template v-if="thaiTrue">--</template>
      <template v-else>
        <el-button text circle @click.stop="setImIcon(row)">
          <el-image class="order__icon" :src="getImIcon(row)" alt="im" />
        </el-button>
      </template>
    </template>
    <template #index="{ row }">{{ row.groupName }}{{ row.index }}</template>
    <template #syncColor="{ row }">
      <template v-if="thaiTrue">--</template>
      <template v-else>
        <el-button text circle @click.stop="setSyncIcon(row)">
          <el-image class="order__icon" :src="getSyncIcon(row)" alt="sync" />
        </el-button>
      </template>
    </template>
    <template #visitColor="{ row }">
      <el-button text circle @click.stop="setVisitIcon(row)">
        <el-image class="order__icon" :src="getVisitIcon(row)" alt="visit" />
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { checkRole } from '@/utils/permission';
import useColumn from './use-column';
import useUserStore from '@/store/modules/user';
import useOrderStore from '@/store/modules/order';
import loveRed from '@/assets/icons/color/love_red.png';
import {
  useResult,
  useCrud,
  useFormDialog,
  useOrderStats,
  useOrderColor,
  useOrderCopy,
  useOrderClone,
  useOrderPassenger,
  useOrderVehicle,
  useOrderQuestion,
  useOrderAssign,
  useOrderUnassign,
  useOrderHotelFormColumn,
  useOrderLogging,
  useOrderTicket,
  useOrderPreview,
  useOrderFormDialog,
  useOrderMatch,
  useOrderDetail,
  useOrderCancel,
  useOrderLogic,
  useOrderComplete,
  useOrderRemark
} from '@/hooks';

dayjs.extend(isBetween);

defineOptions({ name: 'Order' });

// crud引用
const crudRef = ref();

// 展开订单引用
const orderRef = ref();

// 国际化
const { locale, t } = useI18n();

// 添加的订单
const orderStore = useOrderStore();

// 用户状态
const userStore = useUserStore();

// 全选/反选
const toggle = ref(false);

// 全选/反选文本
const toggleText = computed(() => {
  return toggle.value
    ? t('component.app_toolbar.unall')
    : t('component.app_toolbar.all');
});

// 切换全选/反选
const toggleSelection = () => {
  if (!toggle.value) {
    crudRef.value.toggleAllSelection();
  } else {
    crudRef.value.clearSelection();
  }
  toggle.value = !toggle.value;
};

// 泰国角色
const thai = checkRole(['thai']);

// 管理员角色
const admin = checkRole(['admin']);

// 泰国端显示一些功能
const thaiTrue = thai && !admin;

// 泰国端隐藏一些功能
const thaiFalse = !thaiTrue;

// 开发模式
const dev = import.meta.env.DEV;

// 默认选中的选项卡
let activeValue = '1';

// 默认分页
let pageSize = 50;

// 默认搜索条件
const defaultSearch = {
  vehicleTimeRange: [
    dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
    dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')
  ],
  groupId: userStore.groupId,
  sale: undefined,
  status: '1'
};

// 开发模式指定自己的搜索条件
if (dev) {
  activeValue = '1';
  pageSize = 10;
  defaultSearch.vehicleTimeRange = [
    dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')
  ];
  // defaultSearch.sale = '1400818796667614';
  defaultSearch.status = '1';
}

// 激活的选项卡
const active = ref(activeValue);

// 结果展示对话框
const { result, resulting, resulted } = useResult();

// 统计
const { stats, getStats } = useOrderStats();

// 表格列
const { column } = useColumn();

// 大表哥
const {
  canSelect,
  selected,
  form,
  search,
  page,
  loading,
  data,
  option,
  load,
  add,
  edit,
  del,
  select,
  download
} = useCrud({
  url: '/youyang/order',
  column,
  searchLabelWidth: 100,
  searchBtnText: t('order.search.btn.search'),
  emptyBtnText: t('order.search.btn.empty'),
  // 默认条件
  defaultSearch,
  defaultPage: {
    pageSize,
    pageSizes: [10, 20, 30, 40, 50, 100, 200, 300, 400, 500, 1000]
  },
  showHeader: false,
  columnBtn: false,
  gridBtn: false,
  dialogWidth: 800,
  dialogDrag: true,
  searchLabelWidth: locale.value == 'th' ? 'auto' : 90,
  expand: true,
  expandWidth: 40,
  expandFixed: 'right',
  expandClassName: 'order__expand',
  selection: true,
  selectionWidth: 40,
  border: false,
  stripe: false,
  menu: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  before: setParams,
  downloadBefore: setParams,
  after: getStats
});

// 回车搜索
function keydwon(e) {
  if (e.keyCode == 13) {
    load();
  }
}

// 日期范围数组值拆分为2个属性，路线ids逗号分割，车型ids逗号分割
function setParams(params) {
  const { vehicleTimeRange, routeIds, orderVehicleTypIds } = params;
  delete params.vehicleTimeRange;
  if (Array.isArray(vehicleTimeRange) && vehicleTimeRange.length == 2) {
    const [startVehicleTime, endVehicleTime] = vehicleTimeRange;
    params = { ...params, startVehicleTime, endVehicleTime };
  }
  if (routeIds && Array.isArray(routeIds)) {
    params.routeIds = routeIds.join(',');
  }
  if (orderVehicleTypIds && Array.isArray(orderVehicleTypIds)) {
    params.orderVehicleTypIds = orderVehicleTypIds.join(',');
  }
  return params;
}

// 变更tab，客户关怀指定条件
async function tagChange(value) {
  if (value == '70') {
    search.value.passengerWechatColor = 2;
    search.value.visitColor = 0;
    search.value.status = '60';
  } else {
    search.value.passengerWechatColor = undefined;
    search.value.visitColor = undefined;
    search.value.status = value;
  }
  await load();
}

// 切换图标颜色
const {
  getWechatIcon,
  getPhoneIcon,
  getVehicleIcon,
  getImIcon,
  getSyncIcon,
  getVisitIcon,
  setWechatIcon,
  setPhoneIcon,
  setVehicleIcon,
  setImIcon,
  setSyncIcon,
  setVisitIcon,
  setExitPort
} = useOrderColor();

// 复制
const { copy } = useOrderCopy();

// 指派
const { assigner, assigning, assigned } = useOrderAssign({
  url: '/youyang/order/assigns',
  before(assigner) {
    const { orders, vehicle } = assigner;
    const data = orders.map(item => {
      return {
        sale: item.sale,
        driverId: vehicle.driverId,
        driverPhone: vehicle.driverPhone,
        vehicleId: vehicle.id,
        vehicleNumber: vehicle.number
      };
    });
    return data;
  },
  async after(response) {
    await load();
    const { msg, code, data } = response;
    resulting({
      icon: 'success',
      title: '成功',
      subTitle: `${code}，${msg}`,
      messages: data
    });
  }
});

// 导入指派完成
async function assignExcelSuccess(response) {
  await load();
  const { msg, code, data } = response;
  resulting({
    icon: 'success',
    title: '成功',
    subTitle: `${code}，${msg}`,
    messages: data
  });
}

// 取消指派
const { unassigned } = useOrderUnassign({
  url: '/youyang/order/unassign',
  before(row) {
    return { id: row.id };
  },
  after: load
});

// 变更分组
const {
  visible: groupVisible,
  form: groupForm,
  option: groupOption,
  open: groupOpen,
  submit: groupSubmit,
  error: groupError
} = useFormDialog({
  url: '/youyang/order/changeGroup',
  beforeOpen() {
    const ids = selected.value.map(item => item.id);
    return { ids };
  },
  column: [
    {
      label: '新分组',
      prop: 'newGroupId',
      align: 'center',
      span: 24,
      type: 'select',
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/youyang/orderGroup/all',
      props: { res: 'data', label: 'name', value: 'id' },
      rules: [{ required: true, message: '必选的' }]
    }
  ],
  before(params) {
    params.ids = params.ids.join(',');
    return params;
  },
  async after(response) {
    await load();
    const { msg, code, data } = response;
    resulting({
      icon: 'success',
      title: '成功',
      subTitle: `${code}，${msg}`,
      messages: data
    });
  }
});

// 导出：用车订单_财务
function exportFinance() {
  download({ exportType: 'finance' }, '用车订单_财务');
}

// 导出：用车订单_车辆同步
function exportVehicleSync() {
  download({ exportType: 'vehicleSync' }, '用车订单_车辆同步');
}

// 克隆
const { clone } = useOrderClone({
  url: '/youyang/order/clone',
  before(row) {
    return { id: row.id };
  },
  after: load
});

// 乘客信息
const { passenger, passengerDetail } = useOrderPassenger();

// 车辆信息
const { vehicle, vehicleDetail } = useOrderVehicle();

// 问题总结
const { question, questioning } = useOrderQuestion();

// 修改简称（泰国），表单定义
const { column: hotelFormColumn } = useOrderHotelFormColumn();

// 修改简称（泰国）
const {
  visible: hotelVisible,
  form: hotelForm,
  option: hotelOption,
  open: hotelOpen,
  submit: hotelSubmit,
  error: hotelError
} = useFormDialog({
  url: '/youyang/hotel/thai',
  column: hotelFormColumn,
  beforeOpen(data) {
    const { hotelType } = data;
    const id = data[`hotel${hotelType}Id`];
    const name = data[`hotel${hotelType}Name`];
    const shortName = data[`hotel${hotelType}ShortName`];
    const shortNameThai = data[`hotel${hotelType}ShortNameThai`];
    const level = data[`hotel${hotelType}Level`];
    const address = data[`hotel${hotelType}Address`];
    const lonLat = data[`hotel${hotelType}LonLat`];
    return {
      id,
      name,
      shortName,
      shortNameThai,
      level,
      address,
      lonLat,
      hotelType,
      orderId: data.id
    };
  },
  async after() {
    await orderRef.value.load();
    await load();
  }
});

// 操作日志
const { logger, logging } = useOrderLogging();

// 专车打印、举牌打印
const { previewer, previewing, previewed } = useOrderPreview();

// 门票
const { ticketer, ticketing } = useOrderTicket();

// 查看
const { detailer, detailing } = useOrderDetail();

// 变更归属人
const {
  visible: ownerVisible,
  form: ownerForm,
  option: ownerOption,
  open: ownerOpen,
  submit: ownerSubmit,
  error: ownerError
} = useFormDialog({
  url: '/youyang/order/changeOwner',
  beforeOpen(data) {
    const ids = [data.id];
    return { ids };
  },
  column: [
    {
      label: '新归属人',
      prop: 'newOwnerId',
      align: 'center',
      span: 24,
      type: 'select',
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + userStore.token },
      dicUrl: '/api/system/user/list',
      dicQuery: { pageSize: 500 },
      props: { res: 'rows', label: 'nickName', value: 'userId' },
      rules: [{ required: true, message: '必选的' }]
    }
  ],
  before(params) {
    params.ids = params.ids.join(',');
    return params;
  },
  async after(response) {
    await load();
    const { msg, code, data } = response;
    resulting({
      icon: 'success',
      title: '成功',
      subTitle: `${code}，${msg}`,
      messages: data
    });
  }
});

// 修改订单
const {
  visible: editVisible,
  form: editForm,
  option: editOption,
  open: editiOpen,
  submit: editSubmit,
  error: editError,
  matchCustomerWechat
} = useOrderFormDialog({
  async after() {
    await orderRef.value.load();
  }
});

// 添加了表单直接查这个订单号
watch(
  () => orderStore.added,
  async newValue => {
    console.log(newValue);
    if (newValue) {
      search.value.sale = orderStore.form.sale;
      search.value.vehicleTimeRange = undefined;
      await load();
      orderStore.added = false;
    }
  }
);

// 起点终点地址匹配
const { matcher, matching, matched, setFlightRules } = useOrderMatch({
  url: '/youyang/hotel/match',
  form: editForm,
  option: editOption,
  success(e) {
    const { startHotel, endHotel, form, selectedKey, route } = e;
    const messages = [];
    const { sale } = form;
    const hotelStartName = form[selectedKey.startKey];
    const hotelEndName = form[selectedKey.endKey];
    const startShortName = startHotel.data.shortName;
    const endShortName = endHotel.data.shortName;
    const routeName = route.data.name;
    messages.push(`订单：${sale}`);
    messages.push(`开始：${hotelStartName}，${startShortName}`);
    messages.push(`结束：${hotelEndName}，${endShortName}`);
    messages.push(`路线：${routeName}`);
    resulting({
      icon: 'success',
      title: '匹配成功',
      subTitle: route.name,
      messages
    });
  },
  fail(e) {
    const { startHotel, endHotel, form, selectedKey } = e;
    const messages = [];
    const { sale } = form;
    const foundText = '没有找到这个地址或没有填写简写';
    const hotelStartName = form[selectedKey.startKey];
    const hotelEndName = form[selectedKey.endKey];
    const startShortName = startHotel.data?.shortName ?? foundText;
    const endShortName = endHotel.data?.shortName ?? foundText;
    messages.push(`订单：${sale}`);
    messages.push(`开始：${hotelStartName}，${startShortName}`);
    messages.push(`结束：${hotelEndName}，${endShortName}`);
    resulting({
      icon: 'error',
      title: '匹配失败',
      subTitle: '到地址管理补充完善',
      messages
    });
  }
});

// 接机航班必填
watch(() => editForm.value?.type, setFlightRules);

// 取消
const { canceled } = useOrderCancel({
  url: '/youyang/order/cancel',
  before(row) {
    return { id: row.id };
  },
  after: load
});

// 删除
const { logiced } = useOrderLogic({
  url: '/youyang/order/logic',
  before(rows) {
    return rows.map(item => item.id).join(',');
  },
  after: load
});

// 备注
const { remarker, remarking } = useOrderRemark();

// 备注完成
function remarked() {
  orderRef.value.getRemarks();
}

// 完成
const { completed } = useOrderComplete({
  url: '/youyang/order/complete',
  before(row) {
    return { id: row.id };
  },
  after: load
});

// 单元格样式
function cellClassName({ row, columnIndex }) {
  let className = '';
  if (row.status == '1' || row.status == '3') {
    // 待处理、已指派
    const now = dayjs();
    const target = dayjs(row.vehicleTime);
    const targetOneHourAgo = target.subtract(1, 'hour');
    const show = target <= now;
    const show2 = targetOneHourAgo <= now;

    if (row.type == '10' && show) {
      // 标准按天包车
      className += 'order__orange ';
    } else if (row.type == '8') {
      // 门票活动
      className += 'order__green ';
    } else if (row.type == '1' && show) {
      // 接机
      className += 'order__yellow ';
    } else if (row.type == '2' && show2) {
      // 送机
      className += 'order__red ';
    } else {
      // 其他
      className += 'order__blue ';
    }
  } else if (row.status == '4' || row.status == '5') {
    // 已取消、已删除
    className += 'order__grey ';
  } else {
    // 完成及其他
    className += 'order__lightblue ';
  }
  // 左边框
  if (columnIndex == 14) {
    className += 'order__border-left ';
  }
  // 中国端没有线路，泰国端没有简写
  const notRoute = !row.routeId || row.routeId == '';
  const notShortNameThai =
    !row.hotelStartShortNameThai ||
    row.hotelStartShortNameThai == '' ||
    !row.hotelEndShortNameThai ||
    row.hotelEndShortNameThai == '';
  if (columnIndex >= 14) {
    if ((thaiTrue && notShortNameThai) || (thaiFalse && notRoute)) {
      className += 'order__no-route ';
    }
  }

  return className;
}
</script>

<style lang="scss" scoped>
:deep() {
  .el-table--default .el-table__cell .cell {
    padding: 2px;
  }
}
</style>

<style lang="scss">
.order {
  &__header th.el-table__cell {
    font-weight: normal;
  }

  &__icon {
    height: 16px;
  }

  &__stats {
    margin-right: 20px;
  }

  &__body {
    font-weight: bold;
  }

  &__border-left {
    border-left: thin solid var(--el-color-white);
  }

  &__blue {
    color: white;
    background-color: #0099cc !important;
  }

  &__orange {
    background-color: var(--el-color-warning-light-5) !important;
  }

  &__yellow {
    background-color: #ffc107 !important;
  }

  &__green {
    color: white;
    background-color: green !important;
  }

  &__lightblue {
    background-color: #dce9f9 !important;
  }

  &__grey {
    color: white;
    background-color: #888888 !important;
  }

  &__red {
    color: white;
    background-color: #b11f1e !important;
  }

  &__no-route {
    color: white;
    background-color: #ff5722 !important;
  }

  &__expand {
    .el-icon {
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
  }
}
</style>
