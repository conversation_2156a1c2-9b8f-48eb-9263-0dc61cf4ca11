<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    :before-open="beforeOpen"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #menu-before="{ row }">
      <el-button text type="primary" icon="printer" @click="confirm(row)">
        确认函
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';

defineOptions({ name: 'HotelOrder' });

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyanghotel/order',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 1000,
    dialogDrag: true,
    labelWidth: 100,
    viewBtn: true,
    menuWidth: 300,
    beforeAdd: setParams,
    beforeEdit: setParams
  });

// 回显入住退房日期
function beforeOpen(done, type, loading) {
  if (type == 'edit') {
    form.value.checkInRange = [
      form.value.checkInStartDate,
      form.value.checkInEndDate
    ];
    console.log(form.value);
  }
  done();
}

// 设置入住退房日期
function setParams(_form) {
  const [checkInStartDate, checkInEndDate] = _form.checkInRange;
  _form.checkInStartDate = checkInStartDate;
  _form.checkInEndDate = checkInEndDate;
  return _form;
}

function confirm(row) {
  open(`confirm?id=${row.id}`, '_black');
}
</script>

<style lang="scss" scoped></style>
