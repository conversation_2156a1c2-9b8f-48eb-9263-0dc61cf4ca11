<template>
  <el-dialog
    title="门票"
    :width="860"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    overflow
    v-bind="$attrs"
  >
    <div class="app-order-ticket">
      <app-order-ticket v-bind="order" />
    </div>
    <template #footer>
      <el-button icon="printer" type="primary" @click="previewed">
        打印
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
defineOptions({ name: 'AppOrderTicketDialog', inheritAttrs: 'false' });

const props = defineProps({ order: Object });

const { order } = toRefs(props);

// 新窗口预览
function previewed() {
  open(`/order/ticket?id=${order.value.id}`, '_black');
}
</script>

<style lang="scss" scoped>
.app-order-ticket {
  display: flex;
  justify-content: center;
}
</style>
