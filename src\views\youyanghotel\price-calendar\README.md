# 房价房量日历表

这是一个酒店房价房量日历表组件，用于展示和管理酒店房间的价格和库存信息。

## 功能特性

- 📅 **日历视图**: 以日历形式展示14天的房价房量信息
- 🏨 **房型管理**: 支持多种房型和房间的展示
- 💰 **价格展示**: 清晰展示每日房价
- 📊 **库存管理**: 显示每日可用房量
- 🎨 **周末标识**: 周末日期特殊颜色标识
- 🖱️ **交互操作**: 点击单元格可编辑价格房量
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 页面访问

- 主组件: `/hotel/price-calendar`
- 演示页面: `/hotel/price-calendar-demo`

## 组件结构

```
src/views/youyanghotel/price-calendar/
├── index.vue          # 主组件
├── demo.vue           # 演示页面
└── README.md          # 说明文档
```

## 数据结构

### 房型数据
```javascript
{
  id: 1,
  name: '豪华型Deluxe Room',
  rooms: [
    {
      id: 101,
      name: '豪华-不可退'
    }
  ]
}
```

### 价格数据
```javascript
{
  roomId: {
    price: '1,000.00',
    amount: 5
  }
}
```

## 主要功能

### 1. 日期导航
- 左右箭头按钮切换周期
- 显示当前日期范围

### 2. 酒店选择
- 下拉选择不同酒店
- 动态加载对应房型数据

### 3. 房价房量展示
- 上半部分显示房价（红色）
- 下半部分显示房量（绿色）
- 周末背景色区分

### 4. 交互操作
- 点击价格房量单元格进行编辑
- 鼠标悬停高亮效果

## 样式特点

- 使用Element Plus设计风格
- 清晰的表格布局
- 颜色区分不同信息类型
- 响应式设计

## 技术栈

- Vue 3 Composition API
- Element Plus UI组件库
- Day.js 日期处理
- SCSS样式预处理

## 使用方法

1. 直接访问演示页面查看效果
2. 在其他页面中引入组件使用
3. 根据实际需求修改数据源和交互逻辑

## 扩展功能

可以根据实际业务需求添加以下功能：
- 批量编辑价格房量
- 导入导出数据
- 历史记录查看
- 价格策略设置
- 房态管理
- 预订统计
