<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #body>
      <app-route-vehicle-type-dialog
        v-model="visible"
        :route-id="routeId"
        @comlete="load"
      />
    </template>
    <template #menu-before="{ row }">
      <el-button icon="van" text type="primary" @click="open(row)">
        车型
      </el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import { ref } from 'vue';
import { useCrud } from '@/hooks';
import useColumn from './use-column';

defineOptions({ name: 'ConfRoute' });

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/route',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 640,
    dialogDrag: true,
    labelWidth: 120,
    addBtn: false,
    addRowBtn: true,
    cellBtn: true,
    menuWidth: 220
  });

// 显示管理车型对话框
const visible = ref(false);

// 传入车型对话框的路线id
const routeId = ref();

// 打开车型管理对话框
function open(row) {
  routeId.value = row.id;
  visible.value = true;
}
</script>

<style lang="scss" scoped></style>
