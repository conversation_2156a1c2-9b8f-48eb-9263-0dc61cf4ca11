<template>
  <el-dialog
    :title="conf.title"
    :width="1200"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @open="open"
    @close="close"
  >
    <avue-crud
      v-model="form"
      v-model:search="search"
      :table-loading="loading"
      :data="data"
      :option="option"
      :before-open="beforeOpen"
      @size-change="load"
      @current-change="load"
      @search-change="load"
      @search-reset="load"
      @refresh-change="load"
      @row-save="add"
    >
      <template #expand="{ row }">
        <div v-if="row.id" class="picture" v-viewer>
          <el-image
            v-for="item in row.picture?.split(',')"
            class="picture__item"
            fit="contain"
            :src="`/api${item}`"
          />
        </div>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';
import { computed } from 'vue';

defineOptions({ name: 'AppOrderQuestionDialog', inheritAttrs: 'false' });

const emit = defineEmits(['complete']);

const props = defineProps({ orderId: String, sale: String, mode: String });

const { orderId, sale, mode } = toRefs(props);

const isQuestion = computed(() => {
  return mode.value == 'question';
});

const conf = computed(() => {
  if (isQuestion.value) {
    return {
      name: '问题总结',
      title: '问题总结',
      url: '/youyang/orderQuestion'
    };
  } else {
    return {
      name: '客服备注',
      title: '客服备注',
      url: '/youyang/orderRemark'
    };
  }
});

const { column } = useColumn();

// 大表哥
const { form, search, loading, data, option, load, add } = useCrud({
  name: conf.value.name,
  url: conf.value.url,
  list: '/all',
  column,
  searchLabelWidth: 100,
  columnBtn: false,
  gridBtn: false,
  dialogWidth: 640,
  dialogDrag: true,
  addBtn: true,
  editBtn: false,
  delBtn: false,
  expand: true,
  selection: false,
  menu: false,
  beforeAdd: setParams,
  afterAdd: () => emit('complete')
});

// 设置表单
function beforeOpen(done) {
  if (!form.value) {
    form.value = { orderId: orderId.value, sale: sale.value };
  } else {
    form.value.orderId = orderId.value;
    form.value.sale = sale.value;
  }
  done();
}

// 数组参数逗号分隔
function setParams(_form) {
  const { picture } = _form;
  if (Array.isArray(picture)) {
    _form.picture = picture.join(',');
  }
  return _form;
}

function open() {
  search.value = { orderId: orderId.value };
  load();
}

function close() {
  search.value = [];
  data.value = [];
}
</script>

<style lang="scss" scoped>
.picture {
  display: flex;
  margin: 0 10px;
  gap: 10px;

  &__item {
    border: thin solid var(--el-border-color);
    width: 220px;
    height: 200px;
  }
}
</style>
