import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const column = [
    {
      label: '名称',
      prop: 'name',
      width: 140,
      align: 'center',
      span: 24,
      search: true,
      searchSpan: 4,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: 'Thai',
      prop: 'thai',
      width: 140,
      align: 'center',
      span: 24
    },
    {
      label: '携程',
      prop: 'ctrip',
      width: 140,
      align: 'center',
      span: 24
    },
    {
      label: '等级',
      prop: 'level',
      width: 100,
      align: 'center',
      span: 24
    },

    {
      label: '允许车型',
      prop: 'allowIds',
      minWidth: 320,
      headerAlign: 'center',
      span: 24,
      type: 'select',
      multiple: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/vehicleType/all',
      props: { res: 'data', label: 'name', value: 'id' }
    },
    {
      label: '排序',
      prop: 'sort',
      width: 120,
      align: 'center',
      span: 24,
      type: 'number'
    }
  ];

  return { column };
};
