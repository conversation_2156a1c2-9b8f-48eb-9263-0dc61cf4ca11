import { login, logout, getInfo } from '@/api/login';
import { getToken, setToken, removeToken } from '@/utils/auth';
import defAva from '@/assets/images/profile.jpg';

const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    name: '',
    nickName: '',
    avatar: '',
    roles: [],
    permissions: [],
    groupId: '',
    groupName: ''
  }),
  actions: {
    // 登录
    async login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      const res = await login(username, password, code, uuid);
      setToken(res.token);
      this.token = res.token;
    },
    // 获取用户信息
    async getInfo() {
      const res = await getInfo();
      const user = res.user;
      const avatar =
        user.avatar == '' || user.avatar == null
          ? defAva
          : import.meta.env.VITE_APP_BASE_API + user.avatar;

      this.id = user.userId;
      this.name = user.userName;
      this.nickName = user.nickName;
      this.avatar = avatar;

      if (res.roles && res.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        this.roles = res.roles;
        this.permissions = res.permissions;
      } else {
        this.roles = ['ROLE_DEFAULT'];
      }

      this.groupId = res.groupId;
      this.groupName = res.groupName;
    },
    // 退出系统
    async logOut() {
      await logout(this.token);
      this.token = '';
      this.roles = [];
      this.permissions = [];
      removeToken();
    }
  }
});

export default useUserStore;
