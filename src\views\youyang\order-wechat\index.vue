<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  />
</template>

<script setup>
import { useCrud } from '@/hooks';

defineOptions({ name: 'OrderWechat' });

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/orderWechat',
    column: [
      {
        label: '微信号',
        prop: 'name',
        align: 'center',
        width: 480,
        span: 24,
        rules: [{ required: true, message: '必填的' }],
        search: true,
        searchSpan: 4
      }
    ],
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    labelWidth: 120
  });
</script>

<style lang="scss" scoped></style>
