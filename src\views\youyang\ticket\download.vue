<template>
  <div class="app-container order-download">
    <div>
      <el-date-picker
        v-model="value"
        type="datetimerange"
        value-format="YYYY-MM-DD HH:mm:ss"
        format="YYYY-MM-DD HH:mm:ss"
        :default-value="defautltValue"
        :default-time="defaultTime"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        @change="change"
      />

      <el-button
        class="order-download__btn"
        type="primary"
        @click="download({ exportType: 'thaiData' }, '用车订单_数据')"
      >
        数据导出
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import { useCrud } from '@/hooks';

defineOptions({ name: 'TicketDownload', inheritAttrs: false });

// 默认日期
const defautltValue = [dayjs().startOf('date'), dayjs().endOf('date')];

// 默认时间
const defaultTime = [dayjs().startOf('date'), dayjs().endOf('date')];

// 今天
const today = [
  dayjs().startOf('date').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')
];

// 导出
const { search, download } = useCrud({
  name: '订单',
  url: '/youyang/order'
});

// 选择的日期
const value = ref(today);

// 搜索条件
search.value = {
  startVehicleTime: today[0],
  endVehicleTime: today[1]
};

// 日期变更
function change(e) {
  const [start, end] = e;
  search.value.startVehicleTime = start;
  search.value.endVehicleTime = end;
}
</script>

<style lang="scss" scoped>
.order-download {
  width: 800px;

  &__btn {
    margin-left: 12px;
  }
}
</style>
