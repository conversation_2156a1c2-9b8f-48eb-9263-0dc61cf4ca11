<template>
  <div v-loading="loading" class="showcard-page">
    <template v-if="canShowcard">
      <div class="showcard-page__header">
        <div class="showcard-page__action-bar">
          <el-input
            class="showcard-page__input"
            v-model="search.keyword"
            clearable
            placeholder="บุคกิ๊งนำเบอร"
            @change="change"
          >
          </el-input>
          <el-button
            :loading="loading"
            class="showcard-page__search"
            type="primary"
            clearable
            @click="load"
          >
            ค้นหา
          </el-button>
        </div>
      </div>
      <div class="showcard-page__body">
        <app-order-preview v-for="item in data" v-bind="item" show-picture />
      </div>
    </template>
    <template v-else>无权限</template>
  </div>
</template>

<script setup>
import dayjs from 'dayjs';
import { useOrderShowcard } from '@/hooks';

// 开发模式
const dev = import.meta.env.DEV;

// 开发模式指定日期
const devDateTime = [
  dayjs('2025-08-26 00:00:00').format(formatTempalte),
  dayjs('2025-08-26 23:59:59').format(formatTempalte)
];

// 现在到明天
const nowTomorrow = [
  dayjs().format(formatTempalte),
  dayjs().endOf('date').add(1, 'day').format(formatTempalte)
];

// 格式化模板
const formatTempalte = 'YYYY-MM-DD HH:mm:ss';

// 举牌查询
const { canShowcard, loading, data, search, load } = useOrderShowcard({
  url: '/youyang/order',
  list: '/all',
  defaultSearch: {
    keyword: undefined,
    statuses: '1,3,60',
    startVehicleTime: dev ? devDateTime[0] : nowTomorrow[0],
    endVehicleTime: dev ? devDateTime[1] : nowTomorrow[1]
  }
});

// 关键字变更
function change(e) {
  // 开发模式不变条件
  if (dev) {
    return;
  }
  if (e && e != '') {
    search.value.statuses = undefined;
    search.value.startVehicleTime = dayjs()
      .startOf('date')
      .subtract(1, 'day')
      .format(formatTempalte);
  } else {
    search.value.statuses = '1,3,60';
    search.value.startVehicleTime = dayjs().format(formatTempalte);
  }
}

load();
</script>

<style lang="scss" scoped>
.showcard-page {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px;

    @media (max-width: 640px) {
      margin: 20px;
    }
  }

  &__input {
    width: 660px;
    margin-right: 20px;

    :deep() {
      .el-input__inner {
        font-size: 60px;
        line-height: 60px;
        height: 120px;
      }
    }

    @media (max-width: 640px) {
      width: 230px;

      :deep() {
        .el-input__inner {
          font-size: 20px;
          line-height: 20px;
          height: 40px;
        }
      }
    }
  }

  &__search {
    width: 220px;
    height: 120px;
    font-size: 40px;

    @media (max-width: 640px) {
      width: auto;
      height: 40px;
      font-size: 20px;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
  }
}
</style>
