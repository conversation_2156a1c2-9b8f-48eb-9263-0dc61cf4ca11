import { checkRoleBy, checkPermi } from '@/utils/permission';
import { useCrud } from '@/hooks';

export default optoins => {
  const { url, list, defaultSearch, before, after } = optoins;

  // 泰国端角色，指定的路线
  const canShowcard = checkRoleBy(['ss', 'dd', 'hkt', 'ss-dd-hkt']);

  // 素万
  const hasSS = checkPermi(['youyang:order:ss']);

  // 廊曼
  const hasDD = checkPermi(['youyang:order:dd']);

  // 普吉岛
  const hasHKT = checkPermi(['youyang:order:hkt']);

  // 开始路线名
  const startRouteNames = computed(() => {
    let names = [];
    if (hasSS) {
      names.push('SS');
    }
    if (hasDD) {
      names.push('DD');
    }
    if (hasHKT) {
      names.push('HKT');
    }
    return names.join(',');
  });

  const {
    loading, // 加载中
    search, // 搜索
    data, // 列表数据
    download, // 下载
    load // 加载
  } = useCrud({
    url,
    list,
    defaultSearch: {
      ...defaultSearch,
      // 强制路线条件
      startRouteNames: startRouteNames.value
    },
    before,
    after
  });

  return {
    canShowcard,
    loading,
    data,
    search,
    download,
    load
  };
};
