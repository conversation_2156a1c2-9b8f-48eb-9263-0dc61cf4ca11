import axios from 'axios';

// 创建axios实例
const service = axios.create({
  // url前缀
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 10 * 1000
});

let page = undefined;

// 监听错误
addEventListener('error', e => {
  console.error('后台工作线程错误：', e);
});

// 监听消息
addEventListener('message', async e => {
  await executeCommand(e.data);
});

// 执行命令
async function executeCommand(data) {
  const { command } = data;
  if (command == 'paging') {
    page = data.page;
  } else {
    await polling(data);
  }
}

// 轮询
async function polling(options) {
  // 命令、令牌、url、方法、开始前的延迟，间隔
  const {
    command,
    token,
    url,
    method = 'get',
    first = 4,
    interval = 4
  } = options;
  await delay(first * 1000);
  while (true) {
    try {
      const { data } = await service({
        url,
        method,
        params: { ...page },
        headers: { Authorization: 'Bearer ' + token }
      });
      // 发送消息
      postMessage({ command, data });
    } catch (error) {
      console.error('后台工作线程轮询错误：', error.message);
    } finally {
      await delay(interval * 1000);
    }
  }
}

// 延迟
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
