import tab from './tab';
import auth from './auth';
import cache from './cache';
import modal from './modal';
import download from './download';
import { useDict } from '@/utils/dict';
import { download as requestDownload } from '@/utils/request';
import {
  parseTime,
  resetForm,
  addDateRange,
  handleTree,
  selectDictLabel,
  selectDictLabels
} from '@/utils/ruoyi';

export default function installPlugins(app) {
  // 页签操作
  app.config.globalProperties.$tab = tab;
  // 认证对象
  app.config.globalProperties.$auth = auth;
  // 缓存对象
  app.config.globalProperties.$cache = cache;
  // 模态框对象
  app.config.globalProperties.$modal = modal;
  // 下载文件
  app.config.globalProperties.$download = download;
  // 全局方法挂载
  app.config.globalProperties.useDict = useDict;
  app.config.globalProperties.download = requestDownload;
  app.config.globalProperties.parseTime = parseTime;
  app.config.globalProperties.resetForm = resetForm;
  app.config.globalProperties.addDateRange = addDateRange;
  app.config.globalProperties.handleTree = handleTree;
  app.config.globalProperties.selectDictLabel = selectDictLabel;
  app.config.globalProperties.selectDictLabels = selectDictLabels;
}
