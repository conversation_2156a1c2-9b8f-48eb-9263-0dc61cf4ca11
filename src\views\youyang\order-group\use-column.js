import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const column = [
    {
      label: '名称',
      prop: 'name',
      align: 'center',
      width: 160,
      span: 24,
      rules: [{ required: true, message: '必填的' }],
      search: true,
      searchSpan: 4
    },
    {
      label: '权重',
      prop: 'weight',
      align: 'center',
      width: 160,
      span: 24,
      type: 'number',
      precision: 2,
      max: 100,
      placeholder: '1至100的百分比，所有组加起来不能超过100',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '匹配微信号',
      prop: 'wechatIds',
      minWidth: 320,
      align: 'align',
      span: 24,
      type: 'select',
      multiple: true,
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/orderWechat/all',
      props: { res: 'data', label: 'name', value: 'id' }
    },
    {
      label: '用户',
      prop: 'userIds',
      minWidth: 320,
      align: 'align',
      span: 24,
      type: 'select',
      multiple: true,
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/system/user/list',
      dicQuery: { pageSize: 500 },
      dicFormatter(res) {
        return res.rows.map(item => {
          return { label: item.nickName, value: item.userId.toString() };
        });
      },
      rules: [{ required: true, message: '必填的' }]
    }
  ];
  [
    {
      label: '名称',
      prop: 'name',
      align: 'center',
      width: 160,
      span: 24,
      rules: [{ required: true, message: '必填的' }],
      search: true,
      searchSpan: 4
    },
    {
      label: '权重',
      prop: 'weight',
      align: 'center',
      width: 160,
      span: 24,
      type: 'number',
      precision: 2,
      max: 100,
      placeholder: '1至100的百分比，所有组加起来不能超过100',
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '匹配微信号',
      prop: 'wechatIds',
      minWidth: 320,
      align: 'align',
      span: 24,
      type: 'select',
      multiple: true,
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/orderWechat/all',
      props: { res: 'data', label: 'name', value: 'id' }
    },
    {
      label: '用户',
      prop: 'userIds',
      minWidth: 320,
      align: 'align',
      span: 24,
      type: 'select',
      multiple: true,
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/system/user/list',
      dicQuery: { pageSize: 500 },
      dicFormatter(res) {
        return res.rows.map(item => {
          return { label: item.nickName, value: item.userId.toString() };
        });
      },
      rules: [{ required: true, message: '必填的' }]
    }
  ];

  return { column };
};
