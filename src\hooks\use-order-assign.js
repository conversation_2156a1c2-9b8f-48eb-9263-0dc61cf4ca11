import { reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import request from '@/utils/request';

export default options => {
  const { url, before, after } = options;

  const assigner = reactive({
    // 是否显示对话框
    visible: false,
    // 是否显示对话框(Excel导入)
    visibleExcel: false,
    // 选中的多个订单
    orders: [],
    // 选中的车辆
    vehicle: undefined
  });

  // 打开指派
  function assigning(selected) {
    assigner.orders = selected;
    assigner.visible = true;
  }

  // 提交指派
  async function assigned(row) {
    try {
      assigner.vehicle = row;
      const message = `指派给司机：${row.driverPhone}，车牌：${row.number}\n确认提交吗？`;
      await ElMessageBox({
        title: '提示',
        message,
        type: 'warning',
        showCancelButton: true,
        draggable: true,
        async beforeClose(action, instance, done) {
          try {
            if (action != 'confirm') {
              done();
              return;
            }
            instance.cancelButtonLoading = true;
            instance.confirmButtonLoading = true;
            // 处理参数
            const data = before(assigner);
            const response = await request.put(url, data);
            ElMessage.success(response.msg);
            assigner.visible = false;
            await after(response);
            done();
          } catch (error) {
            console.error(error);
            instance.cancelButtonLoading = false;
            instance.confirmButtonLoading = false;
          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  }

  return { assigner, assigning, assigned };
};
