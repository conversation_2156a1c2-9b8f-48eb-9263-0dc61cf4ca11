import { ElMessage, ElMessageBox } from 'element-plus';
import request from '@/utils/request';

export default options => {
  const { url, before, after } = options;

  // 提交删除状态，逻辑删除
  async function logiced(selected) {
    try {
      const message = `确定要删除吗？`;
      await ElMessageBox({
        title: '提示',
        message,
        type: 'warning',
        showCancelButton: true,
        draggable: true,
        async beforeClose(action, instance, done) {
          try {
            if (action != 'confirm') {
              done();
              return;
            }
            instance.cancelButtonLoading = true;
            instance.confirmButtonLoading = true;
            const path = before(selected);
            const response = await request.delete(`${url}/${path}`);
            ElMessage.success(response.msg);
            await after();
            done();
          } catch (error) {
            console.error(error);
            instance.cancelButtonLoading = false;
            instance.confirmButtonLoading = false;
          }
        }
      });
    } catch (error) {
      console.error(error);
    }
  }

  return { logiced };
};
