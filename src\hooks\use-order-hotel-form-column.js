import { useDict } from '@/utils/dict';

export default () => {
  const { yy_level } = useDict('yy_level');

  const column = [
    {
      label: '名称',
      prop: 'name',
      span: 24,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '名称2',
      prop: 'nameOther',
      span: 24
    },
    {
      label: '简称',
      prop: 'shortName',
      span: 24
    },
    {
      label: '简称(Thai)',
      prop: 'shortNameThai',
      span: 24,
      rules: [{ required: true, message: '必填的' }]
    },
    {
      label: '星级',
      prop: 'level',
      type: 'select',
      dicData: yy_level,
      span: 24
    },
    {
      label: '详细地址',
      prop: 'address',
      span: 24,
      type: 'textarea'
    },
    {
      label: '经纬度',
      prop: 'lonLat',
      span: 24
    }
  ];

  return { column };
};
