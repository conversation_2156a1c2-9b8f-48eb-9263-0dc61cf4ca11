<template>
  <div class="app-container order-upload">
    <div class="order-upload__item order-upload__item--ctrip">
      <p>
        <el-link
          type="primary"
          @click="templateDownload('入住订单导入模板_美团.xlsx')"
        >
          下载模板：美团
        </el-link>
      </p>
      <el-upload
        v-loading="meituan.uploader.loading"
        action="/api/youyanghotel/order/meituan"
        :headers="headers"
        :show-file-list="false"
        accept="xlsx,xls"
        drag
        :on-change="meituan.change"
        :on-progress="meituan.progress"
        :on-success="meituan.success"
        :on-error="meituan.error"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到这里<em>或者单击此处选择文件</em>上传
        </div>
      </el-upload>
      <div class="order-upload__matching"></div>
    </div>
    <div class="order-upload__item"></div>
  </div>
</template>

<script setup>
import useUserStore from '@/store/modules/user';
import { useUpload } from '@/hooks';
import { download } from '@/utils/request';

defineOptions({ name: 'HotelUpload' });

// 用户状态
const user = useUserStore();

const headers = { Authorization: 'Bearer ' + user.token };

const meituan = useUpload();

function templateDownload(name) {
  download(
    '/common/download/resource',
    { resource: `/profile/${name}` },
    name,
    { method: 'get' }
  );
}
</script>

<style lang="scss" scoped>
.order-upload {
  display: flex;
  gap: 20;

  &__item {
    flex: 1 0;
    padding: 20px;
    background-color: var(--el-color-white);

    &--ctrip {
      margin-right: 20px;
    }
  }

  &__matching {
    display: flex;
    flex-direction: column;
    height: 400px;
    overflow: auto;
  }
}
</style>
