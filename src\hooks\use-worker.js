import { ref } from 'vue';
import { ElMessage } from 'element-plus';

// 工作线程
export default options => {
  // 解构选项
  const {
    Worker, // 工作线程类
    command, // 命令
    token, // 令牌
    url, // 请求url
    first, // 首次开始的间隔
    interval, // 间隔
    success // 成功回调
  } = options;

  // 是否运行中，避免重复执行
  const running = ref(false);
  // 工作线程
  let worker = undefined;

  // 开始
  function start() {
    if (running.value) {
      console.log(`已经开始${command}`);
      return;
    }
    console.log(`开始${command}`);
    running.value = true;
    worker = new Worker();
    // 监听消息
    worker.addEventListener('message', e => {
      const { data } = e.data;
      if (e.data.command != command) {
        return;
      }
      const { code, msg } = data;
      if (code != 200) {
        ElMessage.error(msg);
        return;
      }
      // 获得处理结果并回调
      if (success && typeof success == 'function') {
        success(data);
      }
    });
    // 发送消息
    worker.postMessage({
      command,
      token,
      url,
      first,
      interval
    });
  }

  // 停止
  function stop() {
    if (!running.value) {
      console.log(`已经停止${command}`);
      return;
    }
    console.log(`停止${command}`);
    running.value = false;
    worker.terminate();
  }

  // 变更分页参数
  function paging(page) {
    if (running.value) {
      worker.postMessage({
        command: 'paging',
        page
      });
    }
  }

  return { running, start, stop, paging };
};
