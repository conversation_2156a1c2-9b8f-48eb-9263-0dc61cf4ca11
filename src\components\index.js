// svg图标
import 'virtual:svg-icons-register';
// element-plus图标库
import * as icons from '@element-plus/icons-vue';
import SvgIcon from '@/components/SvgIcon';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';
import Editor from '@/components/Editor';
import FileUpload from '@/components/FileUpload';
import ImageUpload from '@/components/ImageUpload';
import ImagePreview from '@/components/ImagePreview';
import TreeSelect from '@/components/TreeSelect';
import DictTag from '@/components/DictTag';
import VueQrcode from 'vue-qrcode';
import AppResultDialog from '@/components/AppResultDialog';
import AppOrder from '@/components/AppOrder';
import AppOrderToolbar from '@/components/AppOrderToolbar';
import AppOrderPassengerDialog from '@/components/AppOrderPassengerDialog';
import AppOrderVehicleDialog from '@/components/AppOrderVehicleDialog';
import AppOrderQuestionDialog from '@/components/AppOrderQuestionDialog';
import AppOrderAssignDialog from '@/components/AppOrderAssignDialog';
import AppOrderAssignExcelDialog from '@/components/AppOrderAssignExcelDialog';
import AppOrderGroupDialog from '@/components/AppOrderGroupDialog';
import AppOrderOwnerDialog from '@/components/AppOrderOwnerDialog';
import AppOrderHotelFormDialog from '@/components/AppOrderHotelFormDialog';
import AppOrderLoggingDialog from '@/components/AppOrderLoggingDialog';
import AppOrderPreview from '@/components/AppOrderPreview';
import AppOrderPreviewChinese from '@/components/AppOrderPreviewChinese';
import AppOrderPreviewDialog from '@/components/AppOrderPreviewDialog';
import AppOrderDetail from '@/components/AppOrderDetail';
import AppOrderDetailDialog from '@/components/AppOrderDetailDialog';
import AppOrderForm from '@/components/AppOrderForm';
import AppOrderFormDialog from '@/components/AppOrderFormDialog';
import AppOrderHotelMatcherDialog from '@/components/AppOrderHotelMatcherDialog';
import AppOrderHotelSelectionDialog from '@/components/AppOrderHotelSelectionDialog';
import AppOrderRemarkDialog from '@/components/AppOrderRemarkDialog';
import AppOrderTicket from '@/components/AppOrderTicket';
import AppOrderTicketDialog from '@/components/AppOrderTicketDialog';
import AppHotel from '@/components/AppHotel';
import AppRouteVehicleTypeDialog from '@/components/AppRouteVehicleTypeDialog';
import AppVehiclePicture from '@/components/AppVehiclePicture';
import AppVehicleAuditDialog from '@/components/AppVehicleAuditDialog';
import AppHotelOrderConfirm from '@/components/AppHotelOrderConfirm';

export default {
  install(app) {
    for (const key in icons) {
      const componentConfig = icons[key];
      app.component(componentConfig.name, componentConfig);
    }
    // 图标
    app.component('SvgIcon', SvgIcon);
    // 分页组件
    app.component('Pagination', Pagination);
    // 自定义表格工具组件
    app.component('RightToolbar', RightToolbar);
    // 富文本组件
    app.component('Editor', Editor);
    // 文件上传组件
    app.component('FileUpload', FileUpload);
    // 图片上传组件
    app.component('ImageUpload', ImageUpload);
    // 图片预览组件
    app.component('ImagePreview', ImagePreview);
    // 自定义树选择组件
    app.component('TreeSelect', TreeSelect);
    // 字典标签组件
    app.component('DictTag', DictTag);
    // 二维码组件
    app.component('VueQrcode', VueQrcode);
    // 结果对话框
    app.component(AppResultDialog.name, AppResultDialog);
    // 订单展开
    app.component(AppOrder.name, AppOrder);
    // 订单工具栏
    app.component(AppOrderToolbar.name, AppOrderToolbar);
    // 订单乘客信息对话框
    app.component(AppOrderPassengerDialog.name, AppOrderPassengerDialog);
    // 订单车辆信息对话框
    app.component(AppOrderVehicleDialog.name, AppOrderVehicleDialog);
    // 订单问题总结对话框
    app.component(AppOrderQuestionDialog.name, AppOrderQuestionDialog);
    // 订单批量指派对话框
    app.component(AppOrderAssignDialog.name, AppOrderAssignDialog);
    // 订单批量指派对话框(导入)
    app.component(AppOrderAssignExcelDialog.name, AppOrderAssignExcelDialog);
    // 订单批量修改分组对话框
    app.component(AppOrderGroupDialog.name, AppOrderGroupDialog);
    // 订单批量修改归属人对话框
    app.component(AppOrderOwnerDialog.name, AppOrderOwnerDialog);
    // 订单添加/修改简称(泰国端)
    app.component(AppOrderHotelFormDialog.name, AppOrderHotelFormDialog);
    // 订单操作历史对话框
    app.component(AppOrderLoggingDialog.name, AppOrderLoggingDialog);
    // 订单打印预览
    app.component(AppOrderPreview.name, AppOrderPreview);
    // 订单打印预览(中文)
    app.component(AppOrderPreviewChinese.name, AppOrderPreviewChinese);
    // 订单打印预览对话框
    app.component(AppOrderPreviewDialog.name, AppOrderPreviewDialog);
    // 订单查看
    app.component(AppOrderDetail.name, AppOrderDetail);
    // 订单查看对话框
    app.component(AppOrderDetailDialog.name, AppOrderDetailDialog);
    // 订单表单
    app.component(AppOrderForm.name, AppOrderForm);
    // 订单表单对话框
    app.component(AppOrderFormDialog.name, AppOrderFormDialog);
    // 订单地址匹配对话框
    app.component(AppOrderHotelMatcherDialog.name, AppOrderHotelMatcherDialog);
    // 订单地址选择对话框
    app.component(
      AppOrderHotelSelectionDialog.name,
      AppOrderHotelSelectionDialog
    );
    // 订单客服备注
    app.component(AppOrderRemarkDialog.name, AppOrderRemarkDialog);
    // 订单门票
    app.component(AppOrderTicket.name, AppOrderTicket);
    // 订单门票查看对话框
    app.component(AppOrderTicketDialog.name, AppOrderTicketDialog);
    // 地址项
    app.component(AppHotel.name, AppHotel);
    // 路线关联车型对话框
    app.component(AppRouteVehicleTypeDialog.name, AppRouteVehicleTypeDialog);
    // 车辆照片
    app.component(AppVehiclePicture.name, AppVehiclePicture);
    // 车辆审核对话框
    app.component(AppVehicleAuditDialog.name, AppVehicleAuditDialog);
    // 入住订单确认函
    app.component(AppHotelOrderConfirm.name, AppHotelOrderConfirm);
  }
};
