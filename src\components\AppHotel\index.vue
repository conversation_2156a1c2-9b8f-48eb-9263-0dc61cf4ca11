<template>
  <div class="app-hotel">
    <div class="app-hotel__row">
      <div class="app-hotel__cell">
        <span class="app-hotel__label">名称：</span>
        <span>{{ name }}</span>
      </div>
    </div>
    <div class="app-hotel__row">
      <div class="app-hotel__cell">
        <span class="app-hotel__label">简称：</span>
        <span>{{ shortName }}</span>
      </div>
      <div class="app-hotel__cell">
        <span class="app-hotel__label">Thai：</span>
        <span>{{ shortNameThai }}</span>
      </div>
    </div>
    <div class="app-hotel__row">
      <div class="app-hotel__cell">
        <span class="app-hotel__label">地址：</span>
        <span>{{ address }}</span>
      </div>
    </div>
    <div class="app-hotel__row">
      <div class="app-hotel__cell">
        <span class="app-hotel__label">星级：</span>
        <span> {{ level }}</span>
      </div>
      <div class="app-hotel__cell">
        <span class="app-hotel__label">经纬度：</span>
        <span>{{ lonLat }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({ name: 'AppHotel', inheritAttrs: false });

defineProps({
  name: String,
  nameOther: String,
  shortName: String,
  shortNameThai: String,
  address: String,
  level: String,
  lonLat: String
});
</script>

<style lang="scss" scoped>
.app-hotel {
  display: flex;
  flex-direction: column;

  &__row {
    display: flex;
  }

  &__cell {
    padding-right: 1em;
  }

  &__label {
    color: var(--el-text-color-placeholder);
  }
}
</style>
