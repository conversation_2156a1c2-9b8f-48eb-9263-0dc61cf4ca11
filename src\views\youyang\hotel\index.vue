<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @on-load="load"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
  >
    <template #info="{ row }">
      <app-hotel v-bind="row" />
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useColumn from './use-column';

defineOptions({ name: 'ConfHotel' });

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyang/hotel',
    column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 840,
    dialogDrag: true,
    labelWidth: 120
  });
</script>

<style lang="scss" scoped></style>
