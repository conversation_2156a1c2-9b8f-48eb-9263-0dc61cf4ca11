<template>
  <el-dialog
    title="司机信息"
    :width="1100"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @open="open"
    @close="close"
  >
    <div v-loading="loading" class="app-order-vehicle">
      <div class="app-order-vehicle__row">
        <div class="app-order-vehicle__cell">
          <span class="app-order-vehicle__label">电话：</span>
          <span>{{ data?.driverPhone }}</span>
        </div>
        <div class="app-order-vehicle__cell">
          <span class="app-order-vehicle__label">车辆车牌：</span>
          <span>{{ data?.number }}</span>
        </div>
        <div class="app-order-vehicle__cell">
          <span class="app-order-vehicle__label">车型：</span>
          <span>{{ data?.typeName }}</span>
        </div>
      </div>
      <div class="app-order-vehicle__row">
        <div class="app-order-vehicle__cell">
          <span class="app-order-vehicle__label">车队：</span>
          <span>{{ data?.driverGroupName }}</span>
        </div>
        <div class="app-order-vehicle__cell">
          <span class="app-order-vehicle__label">品牌：</span>
          <dict-tag
            :value="data?.brand"
            :options="yy_brand"
            style="display: inline"
          />
        </div>
        <div class="app-order-vehicle__cell">
          <span class="app-order-vehicle__label">颜色：</span>
          <dict-tag
            :value="data?.color"
            :options="yy_color"
            style="display: inline"
          />
        </div>
      </div>
      <div class="app-order-vehicle__row app-order-vehicle__row--picture">
        <app-vehicle-picture v-bind="data" wrap />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { toRefs } from 'vue';
import { useDict } from '@/utils/dict';
import { useDetails } from '@/hooks';

defineOptions({ name: 'AppOrderVehicleDialog', inheritAttrs: 'false' });

const props = defineProps({ vehicleId: String });

const { vehicleId } = toRefs(props);

const { yy_brand, yy_color } = useDict('yy_brand', 'yy_color');

// 详情
const { loading, data, load } = useDetails({
  // 传递ref的引用，不然useDetails加载时机不对，导致vehicleId.value传递的是undefined
  id: vehicleId,
  url: '/youyang/vehicle'
});

// 打开后加载
function open() {
  load();
}

function close() {
  data.value = {};
}
</script>

<style lang="scss" scoped>
.app-order-vehicle {
  display: flex;
  flex-direction: column;
  border: thin solid var(--el-border-color);
  color: var(--el-text-color-primary);
  font-size: var(--el-font-size-base);
  font-weight: bold;

  &__row {
    display: flex;
    border-bottom: thin solid var(--el-border-color);

    &:last-child {
      border-bottom: none;
    }

    &--picture {
      padding: 10px 0;
    }
  }

  &__cell {
    flex: 1 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 6px 12px;
    border-right: thin solid var(--el-border-color);

    &:last-child {
      border-right: none;
    }
  }

  &__label {
    flex: 0 0 auto;
    display: flex;
    justify-content: end;
    align-items: center;
    min-width: 80px;
    color: var(--el-text-color-secondary);
  }
}
</style>
