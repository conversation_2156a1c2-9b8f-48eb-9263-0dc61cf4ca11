<template>
  <el-dialog
    title="车型管理"
    :width="1000"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @open="open"
    @close="close"
  >
    <avue-crud
      v-model="form"
      v-model:search="search"
      v-model:page="page"
      :table-loading="loading"
      :data="data"
      :option="option"
      :before-open="beforeOpen"
      @size-change="load"
      @current-change="load"
      @search-change="load"
      @search-reset="load"
      @refresh-change="load"
      @row-save="add"
      @row-update="edit"
      @row-del="del"
    >
    </avue-crud>
  </el-dialog>
</template>

<script setup>
import { toRefs } from 'vue';
import { useCrud } from '@/hooks';
import useColumn from './use-column';

defineOptions({ name: 'AppRouteVehicleTypeDialog', inheritAttrs: false });

const props = defineProps({ routeId: String });

const { routeId } = toRefs(props);

const { column } = useColumn();

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    name: '车型',
    url: '/youyang/routeVehicleType',
    column,
    columnBtn: false,
    gridBtn: false,
    expand: false,
    selection: false,
    dialogWidth: 400
  });

// 设置表单的路线id
function beforeOpen(done) {
  if (!form.value) {
    form.value = { routeId: routeId.value };
  } else {
    form.value.routeId = routeId.value;
  }
  done();
}

function open() {
  search.value = { routeId: routeId.value };
  load();
}

function close() {
  data.value = [];
}
</script>

<style lang="scss" scoped></style>
