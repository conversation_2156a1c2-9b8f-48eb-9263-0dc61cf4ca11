<template>
  <el-dialog
    title="乘客信息"
    :width="460"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
  >
    <div class="app-order-passenger">
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">姓名：</span
        ><span>{{ passengerName }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">航班号：</span>
        <span>{{ flight }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">订单类型：</span>
        <dict-tag
          :value="type"
          :options="yy_order_type"
          style="display: inline"
        />
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label"> 订单价格： </span>
        <span>{{ price }}元</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">里程：</span>
        <span>{{ km }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">接车时间：</span>
        <span>{{ vehicleTime?.slice(0, 16) }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">起点：</span>
        <span>{{ hotelStartName }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">终点：</span>
        <span>{{ hotelEndName }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">人数：</span>
        <span>成人: {{ number }}，</span>
        <span>儿童: {{ childrenNumber }}，</span>
        <span>行李: {{ baggageNumber }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">电话：</span>
        <span>{{ passengerPhone }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">微信：</span>
        <span>{{ passengerWechat }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">特殊要求：</span>
        <span>{{ passengerSpecialRemark }}</span>
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">订单来源：</span>
        <dict-tag
          :value="source"
          :options="yy_order_source"
          style="display: inline"
        />
      </div>
      <div class="app-order-passenger__item">
        <span class="app-order-passenger__label">备注：</span>
        <span>{{ passengerRemark }}</span>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
defineOptions({ name: 'AppOrderPassengerDialog', inheritAttrs: 'false' });

import { useDict } from '@/utils/dict';

defineProps({
  price: [Number, String],
  type: String,
  source: String,
  passengerName: String,
  passengerPinyin: String,
  passengerPhone: String,
  passengerWechat: String,
  passengerSpecialRemark: String,
  passengerRemark: String,
  number: Number,
  childrenNumber: Number,
  baggageNumber: Number,
  km: Number,
  flight: String,
  hotelStartName: String,
  hotelEndName: String,
  vehicleTime: String
});

const { yy_order_type, yy_order_source } = useDict(
  'yy_order_type',
  'yy_order_source'
);
</script>

<style lang="scss" scoped>
.app-order-passenger {
  display: flex;
  flex-direction: column;
  border: thin solid var(--el-border-color);
  color: var(--el-text-color-primary);
  font-size: var(--el-font-size-base);
  font-weight: bold;

  &__item {
    flex: 1 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 6px 12px;
    border-bottom: thin solid var(--el-border-color);

    &:last-child {
      border-bottom: none;
    }
  }

  &__label {
    flex: 0 0 auto;
    display: flex;
    justify-content: end;
    align-items: center;
    min-width: 80px;
    color: var(--el-text-color-secondary);
  }
}
</style>
