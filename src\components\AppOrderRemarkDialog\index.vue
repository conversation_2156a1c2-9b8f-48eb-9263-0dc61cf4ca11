<template>
  <el-dialog
    title="新增备注"
    :width="600"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    v-bind="$attrs"
    @close="close"
  >
    <avue-form
      v-model="form"
      :option="option"
      @submit="submit"
      @error="error"
    />
  </el-dialog>
</template>

<script setup>
import { toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import request from '@/utils/request';

defineOptions({ name: 'AppOrderRemarkDialog', inheritAttrs: false });

const emit = defineEmits(['complete']);

const props = defineProps({ orderId: String });

const { orderId } = toRefs(props);

const form = ref({ orderId: orderId.value });

const option = ref({
  column: [
    { label: '客服备注', prop: 'remark', span: 24, type: 'textarea', rows: 4 }
  ]
});

async function submit(form, done) {
  try {
    form.orderId = orderId.value;
    const { msg } = await request.post('/youyang/orderRemark', form);
    ElMessage.success(msg);
    emit('complete');
  } catch (error) {
    console.error(error);
  } finally {
    done();
  }
}

function error(err) {
  console.error(err);
}

function close() {
  form.value.remark = undefined;
}
</script>

<style lang="scss" scoped></style>
