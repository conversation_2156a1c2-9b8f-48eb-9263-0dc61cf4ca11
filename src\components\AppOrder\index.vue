<template>
  <div v-loading="loading" class="app-order">
    <!-- 第1行 -->
    <div class="app-order__row">
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.name') }}:
        </span>
        <el-link
          v-if="thaiFalse"
          class="app-order__content"
          type="primary"
          @click="$emit('passenger', data)"
        >
          {{ data.passengerName }}
          <span v-if="hidePinyin" style="margin-left: 12px">
            {{ data.passengerPinyin }}
          </span>
        </el-link>
        <span v-else class="app-order__content" type="primary">
          {{ data.passengerName }}
          <span v-if="hidePinyin" style="margin-left: 12px">
            {{ data.passengerPinyin }}
          </span>
        </span>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.vehicle_time') }}:
        </span>
        <span class="app-order__vehicle-time">
          {{ vehicleTimeFormat }}
        </span>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.route') }}:
        </span>
        <span>{{ data.routeName }}</span>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.product_name') }}:
        </span>
        <span>{{ data.productName }}</span>
      </div>
    </div>
    <!-- 第2行 -->
    <div class="app-order__row">
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.number') }}:
        </span>
        <span>{{ t('component.app_order.adult') }}: {{ data.number }}，</span>
        <span>
          {{ t('component.app_order.children') }}: {{ data.childrenNumber }}，
        </span>
        <span>
          {{ t('component.app_order.baggage') }}: {{ data.baggageNumber }}</span
        >
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.flight') }}:</span
        >
        <span>{{ data.flight }}</span>
        <span class="app-order__airport">{{ data.airport }}</span>
        <el-radio-group
          v-model="row.exitPort"
          class="app-order__exit-port"
          @change="$emit('change-exit-port', $event)"
        >
          <el-radio value="T1">T1</el-radio>
          <el-radio v-if="hideT2" value="T2">T2</el-radio>
        </el-radio-group>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.vehicle_number') }}:</span
        >
        <el-link type="primary" @click="$emit('driver', data)">
          {{ data.vehicleNumber }}
        </el-link>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.product_package') }}:</span
        >
        <span>{{ data.productPackage }}</span>
      </div>
    </div>
    <!-- 第3行 -->
    <div class="app-order__row">
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.passenger_phone') }}:
        </span>
        <span>{{ data.passengerPhone }}</span>
        <span class="app-order__label app-order__label--th">
          &emsp;{{ t('component.app_order.passenger_wechat') }}:
        </span>
        <span>{{ data.passengerWechat }}</span>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.arrived_time') }}:
        </span>
        <span class="app-order__actual">
          {{ data.arrivedTime }}
        </span>
        <el-button type="warning" size="small" @click="changeArrivedTime">
          {{ t('component.app_order.arrived_edit') }}
        </el-button>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.vehicle_type_name') }}:
        </span>
        <span type="primary">{{ data.vehicleTypeName }}</span>
        <span class="app-order__label app-order__label--th">
          <el-button circle text @click="$emit('change-vehicle-color', row)">
            <el-image
              class="app-order__icon"
              :src="getVehicleIcon(row)"
              alt="vehcile"
            />
          </el-button>
        </span>
      </div>
      <div class="app-order__cell"></div>
    </div>
    <!-- 第4行 -->
    <div class="app-order__row">
      <div class="app-order__cell">
        <span v-if="thaiFalse" class="app-order__label app-order__label--th">
          {{ t('component.app_order.question') }}:
        </span>
        <el-button
          v-if="thaiFalse"
          type="primary"
          size="small"
          @click="$emit('question', data)"
        >
          {{ t('component.app_order.question') }}
        </el-button>
      </div>
      <div class="app-order__cell">
        <div class="app-order__label app-order__label--th">
          {{ t('component.app_order.customer_remark') }}:
        </div>
        <div v-if="thaiFalse" class="app-order__remark">
          <div v-for="item in customerRemarks">
            {{ item.createTime }} {{ item.createBy }}: {{ item.remark }}
          </div>
        </div>
        <el-button
          v-else
          size="small"
          type="primary"
          @click="$emit('remark', data)"
        >
          {{ t('component.app_order.customer_remark') }}
        </el-button>
      </div>
      <div class="app-order__cell">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.driver_phone') }}:
        </span>
        <span>{{ data.driverPhone }}</span>
      </div>
      <div class="app-order__cell"></div>
    </div>
    <!-- 第5行 -->
    <div class="app-order__footer">
      <div class="app-order__status">
        <span class="app-order__label app-order__label--th">
          {{ t('component.app_order.status') }}:
        </span>
        <dict-tag
          class="app-order__tag"
          :value="data.status"
          :options="yy_order_status"
        />
        <el-button-group>
          <el-button
            v-if="enabled.assign"
            type="primary"
            icon="pointer"
            @click="$emit('assign', data)"
          >
            {{ t('component.app_order.assign') }}
          </el-button>
          <el-button
            v-if="thaiFalse"
            type="primary"
            @click="$emit('unassign', data)"
          >
            {{ t('component.app_order.unassign') }}
          </el-button>
          <el-button
            v-if="thaiFalse"
            type="primary"
            icon="copyDocument"
            @click="$emit('clone', data)"
          >
            {{ t('component.app_order.clone') }}
          </el-button>
        </el-button-group>
      </div>
      <el-space class="app-order__actions">
        <el-button-group v-if="thaiTrue" style="margin-right: 12px">
          <el-button
            type="warning"
            :title="t('component.app_order.hotel_start_title')"
            @click="$emit('hotel-start', { hotelType: 'Start', ...data })"
          >
            {{ t('component.app_order.hotel_start') }}
          </el-button>
          <el-button
            type="warning"
            :title="t('component.app_order.hotel_end_title')"
            @click="$emit('hotel-end', { hotelType: 'End', ...data })"
          >
            {{ t('component.app_order.hotel_end') }}
          </el-button>
        </el-button-group>
        <el-button type="primary" @click="$emit('log', data)">
          {{ t('component.app_order.log') }}
        </el-button>
        <el-button-group>
          <el-button type="primary" @click="$emit('preview', data)">
            {{ t('component.app_order.print') }}
          </el-button>
          <el-button
            v-if="thaiFalse"
            type="primary"
            @click="$emit('preview-chinese', data)"
          >
            {{ t('component.app_order.print_showcard') }}
          </el-button>
          <el-button
            v-if="enabled.ticket"
            type="success"
            @click="$emit('ticket', data)"
          >
            {{ t('component.app_order.ticket') }}
          </el-button>
        </el-button-group>
        <el-button
          v-if="thaiFalse"
          type="primary"
          @click="$emit('detail', data)"
        >
          {{ t('component.app_order.detail') }}
        </el-button>
        <el-button-group>
          <el-button
            v-if="thaiFalse"
            type="warning"
            @click="$emit('own', data)"
          >
            {{ t('component.app_order.owner') }}
          </el-button>
          <el-button
            v-if="thaiFalse && enabled.edit"
            type="warning"
            @click="$emit('edit', data)"
          >
            {{ t('component.app_order.update') }}
          </el-button>
          <el-button
            v-if="thaiFalse"
            type="warning"
            @click="$emit('cancel', data)"
          >
            {{ t('component.app_order.cancelled') }}
          </el-button>
          <el-button
            v-if="thaiFalse && enabled.del"
            type="warning"
            @click="$emit('logic', data)"
          >
            {{ t('component.app_order.deleted') }}
          </el-button>
          <el-button
            v-if="thaiFalse"
            type="warning"
            @click="$emit('remark', data)"
          >
            {{ t('component.app_order.remark') }}
          </el-button>
        </el-button-group>
        <el-button
          v-if="enabled.complete"
          type="success"
          icon="finished"
          @click="$emit('complete', data)"
        >
          {{ t('component.app_order.completed') }}
        </el-button>
      </el-space>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';
import { $DialogForm } from '@smallwei/avue';
import { checkRole } from '@/utils/permission';
import { useDict } from '@/utils/dict';
import { useDetails, useOrderColor } from '@/hooks';
import request from '@/utils/request';

defineOptions({ name: 'AppOrder', inheritAttrs: false });

defineEmits([
  'change-phone-color',
  'change-wechat-color',
  'change-vehicle-color',
  'change-exit-port',
  'passenger',
  'driver',
  'question',
  'assign',
  'unassign',
  'clone',
  'hotel-start',
  'hotel-end',
  'log',
  'preview',
  'preview-chinese',
  'vehicle',
  'detail',
  'own',
  'edit',
  'cancel',
  'logic',
  'remark',
  'complete'
]);

const props = defineProps({
  id: String,
  row: Object
});

const { id } = toRefs(props);

const { appContext } = getCurrentInstance();

const { locale, t } = useI18n();

const th = locale.value == 'th';

const { yy_order_status } = useDict('yy_order_status');

const thai = checkRole(['thai']);

const admin = checkRole(['admin']);

// 泰国端显示一些功能
const thaiTrue = thai && !admin;

// 泰国端隐藏一些功能
const thaiFalse = !thaiTrue;

// 隐藏拼音
const hidePinyin = computed(() => {
  const { passengerName } = data.value;
  const firstChar = passengerName?.charAt(0);
  if (
    (firstChar >= 'A' && firstChar <= 'Z') ||
    (firstChar >= 'a' && firstChar <= 'z')
  ) {
    return false;
  } else {
    return true;
  }
});

// 图标颜色
const { getVehicleIcon } = useOrderColor();

// 详情
const { loading, data, load } = useDetails({
  id: id.value,
  url: '/youyang/order'
});

// 接车时间格式化
const vehicleTimeFormat = computed(() => {
  return dayjs(data.value.vehicleTime).format('YYYY-MM-DD HH:mm');
});

// 按钮启用控制
const enabled = computed(() => {
  const available = ['1', '3'].includes(data.value?.status);
  const ticket = data.value.type == '8';
  return {
    assign: available,
    ticket,
    edit: available,
    del: available,
    complete: data.value?.status == '3'
  };
});

// SS简写开头的没有T2
const hideT2 = computed(() => {
  return data.value.hotelStartShortName != 'SS';
});

// 客服备注
const customerRemarks = ref([]);

// 实际抵达
function changeArrivedTime() {
  let { id, arrivedTime } = data.value;
  $DialogForm(appContext)({
    title: t('component.app_order.arrived_title'),
    width: 460,
    data: { id, arrivedTime },
    option: {
      labelWidth: 100,
      column: [
        {
          label: t('component.app_order.arrived_label'),
          prop: 'arrivedTime',
          maxlength: 30,
          span: 24
        }
      ],
      submitText: t('component.app_order.arrived_submit'),
      emptyText: t('component.app_order.arrived_empty')
    },
    async callback(res) {
      try {
        let { id, arrivedTime } = res.data;
        const { code, msg } = await request.put('/youyang/order/arrivedTime', {
          id,
          arrivedTime
        });
        if (code == 200) {
          ElMessage.success(msg);
        }
        res.close();
        data.value.arrivedTime = arrivedTime;
      } catch (error) {
        console.error(error);
      } finally {
        res.done();
      }
    }
  });
}

// 获得客服备注列表，不分页
async function getRemarks() {
  const { data } = await request.get('/youyang/orderRemark/all', {
    params: { orderId: id.value }
  });
  customerRemarks.value = data;
}

load();

getRemarks();

defineExpose({ load, getRemarks });
</script>

<style lang="scss" scoped>
.app-order {
  display: flex;
  flex-direction: column;
  border: thin solid var(--el-border-color);
  font-weight: bold;

  &__row {
    display: flex;
    border-bottom: thin solid var(--el-border-color);

    &:last-child {
      border-bottom: none;
    }
  }

  &__cell {
    flex: 1 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 8px 12px;
    border-right: thin solid var(--el-border-color);

    &:last-child {
      border-right: none;
    }

    &--remark {
      flex-direction: column;
    }
  }

  &__icon {
    height: 18px;
  }

  &__tag {
    display: inline-block;
    margin-right: 12px;
  }

  &__label {
    flex: 0 0 80px;
    display: flex;
    justify-content: end;
    align-items: center;
    padding-right: 10px;

    &--th {
      flex: 0 1 auto;
    }
  }

  &__content {
    flex: 0 1 auto;
    display: flex;
    justify-content: flex-start;
  }

  &__vehicle-time {
    color: var(--el-color-danger);
  }

  &__airport {
    margin-left: 20px;
  }

  &__exit-port {
    margin-left: 20px;
  }

  &__actual {
    flex: 1 0;
    display: inline-flex;
    color: var(--el-color-danger);
  }

  &__remark {
    color: var(--el-color-danger);
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    background-color: var(--el-color-warning-light-9);
  }

  &__status {
    display: flex;
    align-items: center;
    padding: 8px 12px;
  }

  &__assign {
    margin-left: 20px;
  }

  &__actions {
    display: flex;
    align-items: center;
    padding: 8px 12px;
  }
}
</style>
