<template>
  <div v-loading="loading" class="preview-page">
    <div class="preview-page__header">
      <el-button class="preview-page__print" type="primary" @click="printing">
        打印
      </el-button>
    </div>
    <div class="preview-page__body">
      <template v-for="item in orders">
        <app-order-preview-chinese v-if="chinese" v-bind="item" />
        <app-order-preview v-else v-bind="item" />
      </template>
    </div>
    <el-backtop :bottom="100" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import request from '@/utils/request';

const route = useRoute();

// 要查询的ids
const { ids, sort } = route.query;

// 中文显示
const chinese = Object.hasOwn(route.query, 'chinese');

// 是否加载中
const loading = ref(false);

// 订单列表
const orders = ref([]);

// 获得订单列表
async function load() {
  try {
    loading.value = true;
    // 没有ids不能查询
    if (!ids) {
      return;
    }
    const { data } = await request.get('/youyang/order/all', {
      params: { ids, sort }
    });
    orders.value = data;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
}

function printing() {
  print();
}

load();
</script>

<style lang="scss">
@page {
  margin: 0;
  size: A4 landscape;
}
</style>

<style lang="scss" scoped>
.preview-page {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  &__header {
    display: flex;
    margin: 40px;

    @media print {
      display: none;
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
  }

  &__print {
    width: 100px;
    height: 40px;
    font-size: 20px;
  }
}
</style>
