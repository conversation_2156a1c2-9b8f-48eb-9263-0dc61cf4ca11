<template>
  <el-space>
    <el-button icon="check" type="primary" @click="$emit('toggle-all')">
      {{ toggleText }}
    </el-button>
    <el-button
      :disabled="!canSelect"
      type="warning"
      icon="close"
      @click="$emit('logiced')"
    >
      {{ t('component.app_toolbar.del') }}
    </el-button>
    <el-dropdown
      split-button
      type="primary"
      :disabled="!canSelect"
      @click="$emit('preview-chinese')"
    >
       {{ t('component.app_toolbar.print') }}
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            :disabled="!canSelect"
            type="primary"
            @click="$emit('preview')"
          >
             {{ t('component.app_toolbar.print_showcard') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-button
      v-if="thaiFalse"
      split-button
      :disabled="!canSelect"
      type="primary"
      @click="$emit('assign')"
    >
       {{ t('component.app_toolbar.assigns') }}
    </el-button>
    <el-button
      v-if="china"
      :disabled="!canSelect"
      type="primary"
      @click="$emit('group')"
    >
       {{ t('component.app_toolbar.group') }}
    </el-button>
    <el-button v-if="thaiFalse" type="primary" @click="$emit('assign-excel')">

       {{ t('component.app_toolbar.import_assign') }}
    </el-button>
    <el-dropdown
      v-if="thaiFalse"
      split-button
      type="primary"
      @click="$emit('export-finance')"
    >
       {{ t('component.app_toolbar.export_finance') }}
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            type="primary"
            @click="$emit('export-vehicle-sync')"
          >
             {{ t('component.app_toolbar.export_vehicle_sync') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-button
      v-if="!thaiFalse"
      icon="DocumentCopy"
      type="primary"
      @click="$emit('copy')"
    >
      {{ t('component.app_toolbar.copy') }}
    </el-button>
  </el-space>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

import { checkRole } from '@/utils/permission';

defineOptions({ name: 'AppOrderToolbar', inheritAttrs: false });

defineEmits([
  'toggle-all',
  'logiced',
  'preview-chinese',
  'preview',
  'assign',
  'assign-excel',
  'group',
  'export-finance',
  'export-vehicle-sync',
  'copy'
]);

defineProps({
  toggleText: String,
  canSelect: Boolean
});

const { t } = useI18n();

// 中国角色
const china = checkRole(['china']);

// 泰国角色
const thai = checkRole(['thai']);

// 管理员角色
const admin = checkRole(['admin']);

// 泰国端显示一些功能
const thaiTrue = thai && !admin;

// 泰国端隐藏一些功能
const thaiFalse = !thaiTrue;
</script>

<style lang="scss" scoped></style>
