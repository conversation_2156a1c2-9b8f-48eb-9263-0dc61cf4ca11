export default {
  order: {
    tabs: {
      unassgin: '未指派',
      assigned: '已指派',
      cancelled: '已取消',
      deleted: '已删除',
      completed: '已完成',
      care: '客户关怀',
      question: '问题订单',
      all: '全部订单'
    },
    search: {
      vehicle_time: '接车时间',
      sale: '订单号',
      date_index: '序号',
      group: '分组',
      index: '分组序号',
      order_source: '订单来源',
      order_type: '订单类型',
      passenger: '乘客',
      passenger_phone: '乘客电话',
      route: '路线',
      customer_wechat: '微信',
      vehicle_group: '车队',
      driver_phone: '司机电话',
      vehicle_number: '车牌',
      order_vehicle_type: '订单车型',
      arrived: '是否抵达',
      owner_by: '归属人',
      flight: '航班',
      product_name: '产品名称',
      product_package: '产品套餐',
      sort: '订单排序',
      btn: {
        search: '搜索',
        empty: '清空'
      }
    },
    sort: {
      vehicle_time_asc: '接车时间升序',
      vehicle_time_desc: '接车时间降序',
      dateIndex_asc: '订单序号升序',
      dateIndex_desc: '订单序号降序'
    },
    stats: {
      today: '当日订单总数',
      completed: '已完成',
      cancelled: '已取消',
      remaining: '已剩余'
    }
  },
  dict: {
    yy_order_type: {
      1: '接机',
      2: '送机',
      7: '包车游玩',
      8: '门票活动',
      9: 'VIP通关',
      10: '标准按天包车',
      12: '补拍'
    },
    yy_order_status: {
      1: '待服务',
      3: '已指派',
      4: '已取消',
      5: '已删除',
      60: '已完成'
    }
  },
  component: {
    app_toolbar: {
      all: '全选',
      unall: '反选',
      del: '删除',
      print: '专车打印',
      print_showcard: '举牌手打印',
      assigns: '批量指派',
      group: '修改分组',
      import_assign: '导入指派',
      export_finance: '财务导出',
      export_vehicle_sync: '车辆同步导出',
      copy: '复制'
    },
    app_order: {
      // 第1行
      name: '姓名',
      vehicle_time: '接车时间',
      route: '车辆路线',
      product_name: '产品名称',
      // 第2行
      number: '人数',
      adult: '成人',
      children: '儿童',
      baggage: '行李',
      flight: '抵达航班',
      vehicle_number: '车辆车牌',
      product_package: '产品套餐',
      // 第3行
      passenger_phone: '电话',
      passenger_wechat: '微信',
      arrived_time: '实际抵达',
      arrived_edit: '修改',
      arrived_title: '修改',
      arrived_label: '实际抵达',
      arrived_submit: '提交',
      arrived_empty: '关闭',
      vehicle_type_name: '车辆车型',
      // 第4行
      question: '问题总结',
      customer_remark: '客服备注',
      driver_phone: '司机电话',
      // 第5行
      status: '订单状态',
      assign: '指派',
      unassign: '取消指派',
      clone: '克隆',
      hotel_start: '开始地址',
      hotel_start_title: '添加/修改开始地址',
      hotel_end: '结束地址',
      hotel_end_title: '添加/修改结束地址',
      log: '操作历史',
      print: '专车打印',
      print_showcard: '举牌打印',
      ticket: '门票',
      detail: '查看',
      owner: '归属人',
      update: '修改',
      cancelled: '取消',
      deleted: '删除',
      remark: '备注',
      completed: '完成'
    }
  }
};
