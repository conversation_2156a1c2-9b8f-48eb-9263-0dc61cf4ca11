{"name": "ruoyi", "version": "3.8.8", "description": "管理系统", "author": "epcastle", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build:dev": "vite build --mode development --outDir dist_dev", "build:prod": "vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@smallwei/avue": "^3.6.5", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "dayjs": "^1.11.13", "echarts": "5.5.1", "element-plus": "2.9.0", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lodash": "^4.17.21", "nprogress": "0.2.0", "pinia": "2.1.7", "qs": "^6.14.0", "v-viewer": "^3.0.11", "viewerjs": "^1.11.7", "vue": "3.4.31", "vue-clipboard3": "^2.0.0", "vue-cropper": "1.1.1", "vue-i18n": "^11.1.12", "vue-qrcode": "^2.2.2", "vue-router": "4.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "fast-glob": "^3.3.3", "sass": "1.89.0", "unplugin-auto-import": "19.3.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "7.0.6", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}