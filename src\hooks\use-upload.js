import { reactive } from 'vue';
import { ElMessage } from 'element-plus';

export default (options = {}) => {
  const { afterSuccess } = options;

  const uploader = reactive({
    loading: false,
    percentage: 0,
    indeterminate: false,
    status: ''
  });

  function change(uploadFile) {
    if (uploadFile.status == 'ready') {
      uploader.loading = true;
      uploader.percentage = 0;
    }
  }

  function progress(progress) {
    uploader.percentage = progress.percent;
    if (uploader.percentage == 100) {
      uploader.indeterminate = true;
      uploader.status = 'warning';
    }
  }

  async function success(response) {
    uploader.loading = false;
    uploader.indeterminate = false;
    uploader.status = 'success';
    if (response.code == 200) {
      ElMessage.success(response.msg);
      afterSuccess(response);
    } else {
      ElMessage.error(response.msg);
    }
  }

  function error(error) {
    uploader.loading = false;
    uploader.indeterminate = false;
    uploader.status = 'exception';
    ElMessage.error(error.message);
  }

  return {
    uploader,
    change,
    progress,
    success,
    error
  };
};
