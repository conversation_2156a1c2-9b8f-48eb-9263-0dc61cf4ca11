import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const column = [
    {
      label: '车型',
      prop: 'vehicleTypeId',
      align: 'center',
      hide: true,
      search: true,
      searchSpan: 8,
      span: 24,
      type: 'select',
      filterable: true,
      dicHeaders: { Authorization: 'Bearer ' + user.token },
      dicUrl: '/api/youyang/vehicleType/all',
      props: { res: 'data', label: 'name', value: 'id' },
      rules: [{ required: true, message: '请输入' }]
    },
    {
      label: '车型',
      prop: 'vehicleTypeName',
      minWidth: 200,
      align: 'center',
      display: false
    },
    {
      label: '价格',
      prop: 'price',
      width: 200,
      align: 'center',
      type: 'number',
      formatter(row, value) {
        return `฿${value}`;
      },
      span: 24,
      rules: [{ required: true, message: '请输入' }]
    },
    {
      label: '排序',
      prop: 'sort',
      width: 80,
      align: 'center',
      display: false
    }
  ];

  return { column };
};
