<template>
  <avue-crud
    class="app-container"
    v-model="form"
    v-model:search="search"
    v-model:page="page"
    :table-loading="loading"
    :data="data"
    :option="option"
    @search-change="load"
    @search-reset="load"
    @refresh-change="load"
    @row-save="add"
    @row-update="edit"
    @row-del="del"
    @expand-change="expandChange"
  >
    <template #calender-header>
      <div class="column">
        <div v-for="i in 10" style="margin-left: 80px">
          <div>{{ getWeek(i) }}</div>
          <div>{{ getMonthDate(i) }}</div>
        </div>
      </div>
    </template>

    <template #calender="{ row }">
      <div class="column">
        <div style="margin-left: 80px">
          <div>{{ row }}</div>
        </div>
      </div>
    </template>

    <template #expand="{ row }">
      <div class="product">
        <div class="product__name">{{ row.name }}</div>
        <div class="product__calender">
          <el-button
            class="product__calender-item"
            v-for="item in row.calender"
          >
            <div>{{ item.price }}</div>
            <div>{{ item.amount }}</div>
          </el-button>
        </div>
      </div>
    </template>
  </avue-crud>
</template>

<script setup>
import { useCrud } from '@/hooks';
import useUserStore from '@/store/modules/user';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import useColumn from '@/views/youyang/vehicle/use-column.js';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';

dayjs.extend(localizedFormat);

// 用户状态
const user = useUserStore();
const priceDetails = [
  {
    roomName: '豪华型',
    product: [
      {
        procudctName: '豪华房禁烟',
        calender: [
          {
            day: '9.17',
            price: 1000
          },
          {
            day: '9.18',
            price: 2000
          },
          {
            day: '9.19',
            price: 3000
          }
        ]
      },
      {
        procudctName: '豪华房不禁烟',
        calender: [
          {
            day: '9.17',
            price: 4000
          },
          {
            day: '9.18',
            price: 5000
          },
          {
            day: '9.19',
            price: 6000
          }
        ]
      }
    ]
  },
  {
    roomName: '普通型',
    procudctName: '豪华房不禁烟',
    calender: [
      {
        date: '9-17',
        price: 4000
      },
      {
        date: '9-18',
        price: 5000
      },
      {
        date: '9-19',
        price: 6000
      }
    ]
  }
];

const column = [
  {
    label: '酒店名',
    prop: 'hname',
    align: 'center',
    width: 160,
    search: true,
    searchSpan: 4,
    type: 'select',
    display: false,
    dicHeaders: { Authorization: 'Bearer ' + user.token },
    dicUrl: '/api/youyanghotel/hotel/listAll',
    props: { res: 'data', label: 'hname', value: 'id' },
    hide: true
  },
  {
    label: '全部房型',
    prop: 'name',
    align: 'center',
    width: 160,
    search: true,
    searchSpan: 4,
    display: false
  },
  {
    label: '日历',
    prop: 'calender',
    align: 'center',
    display: false
  }
];

// 大表哥
const { form, search, page, loading, data, option, load, add, edit, del } =
  useCrud({
    url: '/youyanghotel/room/priceAmountCalendar',
    list: '',
    expand: true,
    editBtn: false,
    delBtn: false,
    column: column,
    searchLabelWidth: 100,
    columnBtn: false,
    gridBtn: false,
    dialogWidth: 600,
    dialogDrag: true,
    labelWidth: 120
  });

const expandChange = (row, expendList) => {
  ElMessage.success(row.$index + '展开回调');
};

function getWeek(i) {
  return dayjs().add(i, 'day').format('dddd');
}

function getMonthDate(i) {
  console.log(i);
  return dayjs().add(i, 'day').format('MM-DD');
}
</script>

<style lang="scss" scoped>
.column {
  display: flex;
}

.product {
  display: flex;
  padding: 20px;

  &__name {
    display: flex;
    width: 200px;
    font-size: 28px;
    font-weight: bold;
  }

  &__calender {
    display: flex;

    &-item {
      display: flex;
      align-content: flex-start;
      width: 180px;
      height: 120px;
    }
  }
}
</style>
