import useUserStore from '@/store/modules/user';

export default () => {
  // 用户状态
  const user = useUserStore();

  const propsHttp = { home: '/api', url: 'fileName' };
  const listType = 'picture-card';
  const headers = { Authorization: 'Bearer ' + user.token };
  const action = '/api/common/upload';
  const accept = 'image';
  const fileSize = 10000;
  const tip = '只能上传jpg/png，最多6张，且每张不超过10M';
  const limit = 6;
  const multiple = true;

  const column = [
    {
      label: '订单号',
      prop: 'sale',
      align: 'center',
      width: 180,
      display: false
    },
    {
      label: '操作人',
      prop: 'createBy',
      align: 'center',
      width: 180,
      span: 24,
      display: false
    },
    {
      label: '操作时间',
      prop: 'createTime',
      align: 'center',
      width: 180,
      display: false,
      span: 24
    },
    {
      label: '操作备注',
      prop: 'remark',
      span: 24,
      rules: [{ required: true, message: '必填的' }],
      type: 'textarea',
      rows: 6
    },
    {
      label: '图片',
      prop: 'picture',
      align: 'center',
      width: 120,
      hide: true,
      span: 24,
      type: 'upload',
      fileSize,
      tip,
      limit,
      multiple,
      propsHttp,
      listType,
      headers,
      action,
      accept
    }
  ];

  return { column };
};
