<template>
  <div
    class="app-vehicle-picture"
    :class="{ 'app-vehicle-picture--wrap': wrap }"
    v-viewer
  >
    <div class="app-vehicle-picture__item" v-for="item in list">
      <el-image
        class="app-vehicle-picture__img"
        fit="contain"
        :src="item.src"
      />
      <div>{{ item.title }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

defineOptions({ name: 'AppVehiclePicture', inheritAttrs: false });

const props = defineProps({
  wrap: Boolean,
  licencePicture: String,
  insurancePicture: String,
  peoplePicture: String,
  frontPicture: String,
  bodyPicture: String,
  backPicture: String,
  trunkPicture: String
});

const dev = import.meta.env.DEV;

// 开发版连到正式版链接
const host = dev ? 'http://************' : '';

const list = computed(() => {
  return [
    { title: '驾驶证照片', src: `${host}/api${props.licencePicture ?? ''}` },
    { title: '保险证照片', src: `${host}/api${props.insurancePicture ?? ''}` },
    { title: '人车合照', src: `${host}/api${props.peoplePicture ?? ''}` },
    { title: '车前排照片', src: `${host}/api${props.frontPicture ?? ''}` },
    { title: '车身照片', src: `${host}/api${props.bodyPicture ?? ''}` },
    { title: '车后排照片', src: `${host}/api${props.backPicture ?? ''}` },
    { title: '车后备箱照片', src: `${host}/api${props.trunkPicture ?? ''}` }
  ];
});
</script>

<style lang="scss" scoped>
.app-vehicle-picture {
  display: flex;
  justify-content: flex-start;
  margin: 0 10px;
  gap: 10px;

  &--wrap {
    flex-wrap: wrap;
  }

  &__item {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: thin solid var(--el-border-color);
  }

  &__img {
    width: 220px;
    height: 200px;
  }
}
</style>
