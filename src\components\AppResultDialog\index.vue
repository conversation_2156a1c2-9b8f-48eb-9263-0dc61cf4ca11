<template>
  <el-dialog
    title="结果"
    :width="760"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    draggable
    lock-scroll
    v-bind="$attrs"
  >
    <el-result v-bind="$attrs">
      <template #extra>
        <el-button
          icon="close"
          type="primary"
          @click="$emit('update:model-value', false)"
        >
          关闭
        </el-button>
      </template>
    </el-result>
    <div class="app-result" v-if="messages.length > 0">
      <div class="app-result__item" v-for="message in messages">
        {{ message }}
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
defineOptions({ name: 'AppResultDialog', inheritAttrs: 'false' });

defineProps({ link: String, route: Object, messages: Array });
</script>

<style lang="scss" scoped>
.app-result {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
  max-height: 320px;
  overflow: auto;

  &__item {
    padding: 2px 0;
    white-space: pre;
  }
}
</style>
