import { ref, unref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import request from '@/utils/request';

export default options => {
  const {
    url,
    method = 'put',
    defaultForm,
    beforeOpen,
    afterOpen,
    confirm = true,
    before,
    after
  } = options;

  const visible = ref(false);

  const form = ref(defaultForm);

  const option = ref({ labelWidth: 120, ...options });

  // 打开对话框
  function open(data) {
    // 打开之前回调
    if (beforeOpen && typeof beforeOpen == 'function') {
      data = beforeOpen(data);
    }
    form.value = data;
    visible.value = true;
    // 打开之后回调
    if (afterOpen && typeof afterOpen == 'function') {
      afterOpen(form.value);
    }
  }

  async function submit(_form, done) {
    try {
      // 请求之前回调
      if (before && typeof before == 'function') {
        _form = await before(_form);
      }
      const _url = unref(url);
      if (confirm) {
        await ElMessageBox.confirm('确认提交吗?', '提示');
      }
      const result = await request[method](_url, _form);
      if (result.code != 200) {
        throw new Error(result.msg);
      }
      // 请求之后回调
      if (after && typeof after == 'function') {
        await after(result);
      }
      ElMessage.success(result.msg);
      visible.value = false;
    } catch (error) {
      console.error(error);
    } finally {
      done();
    }
  }

  function error(err) {
    console.error(err);
  }

  return {
    visible,
    form,
    option,
    open,
    submit,
    error
  };
};
