<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>房价房量日历表演示</h1>
      <p>这是一个静态数据演示页面，展示酒店房价房量日历表的功能</p>

      <div class="feature-list">
        <h3>功能特性：</h3>
        <ul>
          <li>📅 14天日历视图展示</li>
          <li>🏨 多房型房间管理</li>
          <li>💰 房价实时显示</li>
          <li>📊 房量库存管理</li>
          <li>🎨 周末特殊标识</li>
          <li>🖱️ 点击编辑功能</li>
        </ul>
      </div>
    </div>

    <!-- 房价房量日历表组件 -->
    <PriceCalendar />
  </div>
</template>

<script setup>
import PriceCalendar from './index.vue'

defineOptions({ name: 'PriceCalendarDemo' })
</script>

<style lang="scss" scoped>
.demo-container {
  min-height: 100vh;
  background: #f5f5f5;
  
  .demo-header {
    background: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 24px;
    }
    
    p {
      margin: 0 0 20px 0;
      color: #666;
      font-size: 14px;
    }

    .feature-list {
      text-align: left;
      max-width: 600px;
      margin: 0 auto;

      h3 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 5px;
          color: #666;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
